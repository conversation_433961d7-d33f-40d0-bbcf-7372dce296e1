# XUI App Server

一个现代化的、生产就绪的 TypeScript Express API 服务器，专为智能对话系统设计，集成了 A2A/A2U 协议支持、PostgreSQL 数据库、完整的监控体系和 Swagger API 文档。

## 🚀 核心功能

### 智能对话系统
- **Agent 管理**: 创建、查询、更新和删除智能代理，支持多种类型和分组
- **会话管理**: 管理用户与 Agent 的对话会话，支持会话历史和元数据
- **消息管理**: 处理会话中的消息记录，支持多种内容类型（文本、文件、数据等）
- **流式聊天**: 基于 SSE 的实时对话功能，支持 A2A/A2U 协议转换和流恢复
- **消息分页**: 高性能的游标分页系统，支持时间顺序消息检索

### 技术特性
- **现代技术栈**: Express 5.x, TypeScript 5.8, Drizzle ORM 0.44, PostgreSQL
- **模块化架构**: 基于 TSyringe 的依赖注入和模块化设计
- **类型安全**: 完整的 TypeScript 支持，严格的类型配置
- **数据验证**: Zod 模式验证请求/响应数据
- **安全防护**: Helmet, CORS, 速率限制, 输入清理
- **结构化日志**: Winston 日志系统，支持多级别和文件输出
- **LLM 可观测性**: 集成 Langfuse，提供全面的 LLM 追踪和分析
- **数据库**: PostgreSQL 连接池管理和数据库迁移
- **API 文档**: Swagger/OpenAPI 3.0 交互式文档
- **测试框架**: Jest 测试框架，支持 TypeScript
- **代码质量**: ESLint 9.x (扁平配置), Prettier 代码格式化
- **健康检查**: 内置健康监控和优雅关闭
- **错误处理**: 全局异常处理和防御性编程
- **开发体验**: 热重载、调试支持、跨平台兼容性

## 📋 系统要求

- **Node.js** >= 20.0.0
- **pnpm** >= 9.0.0
- **PostgreSQL** >= 13
- **Docker** (可选，用于容器化部署)

## 🛠️ 快速开始

### 手动安装

```bash
# 1. 克隆项目
git clone <repository-url>
cd xui-app-server

# 2. 安装依赖
pnpm install

# 3. 设置环境变量
cp .env.example .env.development
# 编辑 .env.development 文件配置你的环境

# 4. 初始化数据库
pnpm db:generate:dev     # 生成迁移文件
pnpm db:migrate:dev      # 应用数据库迁移

# 5. 启动开发服务器
pnpm dev
```

### 快速部署

```bash
# 使用快速部署脚本
pnpm deploy              # 运行快速部署脚本
```

### 环境配置

项目支持多环境配置，优先级如下：
1. `.env.local` - 本地覆盖配置（不提交到版本控制）
2. `.env.{NODE_ENV}` - 环境特定配置（如 `.env.development`）
3. `.env` - 默认配置

**开发环境配置示例** (`.env.development`):

```env
# 应用配置
NODE_ENV=development
PORT=3000
HOST=0.0.0.0

# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=xui
DB_USER=postgres
DB_PASSWORD=postgres
DB_SSL=false

# 认证配置（网关认证模式）
# 无需 JWT 配置，通过 userId 请求头进行认证

# CORS 配置
CORS_ORIGIN=*
CORS_CREDENTIALS=true

# 日志配置
LOG_DIR=logs/
LOG_LEVEL=debug
LOG_FORMAT=dev
ENABLE_FILE_LOGGING=true

# 安全配置
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
BCRYPT_ROUNDS=10

# Langfuse 配置（LLM 可观测性）
LANGFUSE_ENABLED=true
LANGFUSE_PUBLIC_KEY=pk-lf-your-public-key-here
LANGFUSE_SECRET_KEY=sk-lf-your-secret-key-here
LANGFUSE_HOST=https://cloud.langfuse.com
LANGFUSE_ENVIRONMENT=development
LANGFUSE_DEBUG=true
```

## 🚦 使用指南

### 开发命令

```bash
# 开发服务器
pnpm dev                    # 启动开发服务器（热重载）
pnpm start:dev             # 启动开发环境构建版本
pnpm start:prod            # 启动生产环境构建版本

# 构建和清理
pnpm build                 # 构建项目
pnpm clean                 # 清理构建文件
pnpm type-check           # TypeScript 类型检查
```

### 生产部署

```bash
# 快速部署
pnpm deploy               # 运行快速部署脚本

# 快速部署脚本选项
./scripts/quick-deploy.sh                        # 默认部署
```

### 数据库操作

```bash
# 数据库迁移
pnpm db:generate          # 生成新的迁移文件
pnpm db:migrate           # 应用数据库迁移
pnpm db:push              # 推送数据库迁移
pnpm db:studio            # 打开 Drizzle Studio 数据库管理界面

# 环境特定操作
pnpm db:generate:dev      # 开发环境生成迁移
pnpm db:migrate:dev       # 开发环境应用迁移
pnpm db:studio:dev        # 开发环境 Drizzle Studio
pnpm db:generate:prod     # 生产环境生成迁移
pnpm db:migrate:prod      # 生产环境应用迁移
```

### 测试

```bash
# 运行测试
pnpm test:jest            # 运行所有测试
pnpm test:jest:watch      # 监视模式运行测试
pnpm test:jest:coverage   # 运行测试并生成覆盖率报告
```

### 代码质量

```bash
# 代码检查和格式化
pnpm lint                 # 检查代码规范
pnpm lint:fix             # 自动修复代码规范问题
pnpm format               # 格式化代码
pnpm format:check         # 检查代码格式
pnpm type-check           # TypeScript 类型检查
```

## 📁 项目结构

```
xui-app-server/
├── src/                     # 源代码目录
│   ├── app/                 # 应用程序核心
│   │   ├── app.ts           # Express 应用配置
│   │   ├── container.ts     # TSyringe 依赖注入容器
│   │   ├── server.ts        # HTTP 服务器启动
│   │   └── routes/          # API 路由定义
│   │       ├── index.ts     # 主路由器
│   │       ├── agent.ts     # Agent 路由
│   │       ├── session.ts   # Session 路由
│   │       ├── message.ts   # Message 路由
│   │       └── health.ts    # 健康检查路由
│   ├── infrastructure/      # 基础设施层
│   │   ├── config/          # 配置管理
│   │   ├── database/        # 数据库相关
│   │   │   └── schema/      # Drizzle 数据库模式
│   │   │       ├── agent.ts     # Agent 数据表模式
│   │   │       ├── session.ts   # Session 数据表模式
│   │   │       ├── message.ts   # Message 数据表模式
│   │   │       └── index.ts     # 模式导出
│   │   ├── logger/          # 日志系统
│   │   ├── middleware/      # Express 中间件
│   │   ├── monitoring/      # 监控和指标
│   │   └── swagger/         # Swagger API 文档
│   ├── modules/             # 业务模块（模块化架构）
│   │   ├── agent/           # Agent 模块
│   │   │   ├── controllers/ # Agent 控制器
│   │   │   ├── services/    # Agent 服务
│   │   │   ├── validators/  # Agent 验证器
│   │   │   └── types/       # Agent 类型定义
│   │   ├── session/         # Session 模块
│   │   ├── message/         # Message 模块
│   │   └── health/          # 健康检查模块
│   ├── shared/              # 共享资源
│   │   ├── constants/       # 常量定义
│   │   ├── types/           # 通用类型定义
│   │   └── utils/           # 工具函数
│   │       ├── env.ts           # 环境配置加载器
│   │       ├── errors.ts        # 自定义错误类
│   │       ├── message-converter.ts  # A2A/A2U 消息转换
│   │       └── time.ts          # 时间工具函数
│   └── index.ts             # 应用程序入口点
├── scripts/                 # 脚本文件
│   ├── quick-deploy.sh      # 快速部署脚本
│   └── validate-env.ts      # 环境验证脚本
├── tests/                   # 测试文件
│   ├── unit/                # 单元测试
│   └── setup.ts             # 测试设置
├── features/                # BDD 测试特性文件
├── drizzle/                 # 生成的数据库迁移文件
├── logs/                    # 日志文件（运行时生成）
├── dist/                    # 构建输出目录
├── data/                    # 数据文件
├── pids/                    # 进程ID文件（PM2）
├── .env.example             # 环境变量示例
├── ecosystem.config.cjs     # PM2 配置文件
├── drizzle.config.ts        # Drizzle ORM 配置
├── jest.config.js           # Jest 测试配置
├── eslint.config.js         # ESLint 配置
├── tsconfig.json            # TypeScript 配置
├── nodemon.json             # Nodemon 配置
└── package.json             # 项目依赖和脚本
```

## 🔌 API 功能

### 核心 API 端点

#### Agent 管理 (`/api/agent`)
- `GET /api/agent` - 获取所有智能代理（分页、搜索、排序）
- `GET /api/agent/:id` - 根据 ID 获取单个代理
- `POST /api/agent` - 创建新的智能代理
- `PUT /api/agent/:id` - 更新代理信息
- `DELETE /api/agent/:id` - 删除代理

#### 会话管理 (`/api/session`)
- `GET /api/session` - 获取用户所有会话历史（分页、搜索）
- `GET /api/session/agent/:agentId` - 获取与特定代理的会话记录
- `DELETE /api/session/:id` - 删除会话（级联删除消息）
- `GET /api/session/chat` - 与 AI Agent 进行流式聊天交互

#### 消息管理 (`/api/message`)
- `GET /api/message/session/:sessionId` - 获取会话消息列表（游标分页）
- `GET /api/message/:id` - 根据 ID 获取单条消息
- `DELETE /api/message/:id` - 删除单条消息

#### 系统监控 (`/api/health`)
- `GET /api/health` - 基础健康检查
- `GET /api/health/detailed` - 详细健康信息（数据库状态等）

### API 文档

- **Swagger UI**: 运行时可在 `/api-docs` 访问交互式 API 文档
- **OpenAPI 3.0**: 完整的 API 规范和示例

### 认证机制

项目采用**网关认证**架构：
- 🔐 **网关层认证**: 用户通过网关进行身份验证
- 🔄 **请求转发**: 网关在转发请求时添加 `userId` 头部
- ✅ **API 层验证**: API 服务验证 `userId` 头部的存在性
- 🛡️ **权限控制**: 基于 `userId` 进行数据隔离和权限控制

```http
# 请求头示例
userId: your-user-id
Content-Type: application/json
```

### A2A/A2U 协议支持

- **A2U (Agent-to-User)**: 用户与 Agent 交互的标准协议
- **A2A (Agent-to-Agent)**: Agent 之间通信的标准协议
- **协议转换**: 自动在 A2U 和 A2A 格式之间转换
- **流式响应**: 支持 Server-Sent Events (SSE) 实时流式聊天
- **消息类型**: 支持文本、文件、工具调用、工具结果、数据等多种内容类型

### 数据库模型

#### Agent 表
- **基本信息**: 名称、头像、卡片URL
- **分类**: 类型（创作协作类/沉浸式教学类）、分组（AI老师/AI对练/Leader Mate）
- **权限**: 目标用户（学员/管理员）、状态（正常/下架/已删除）
- **扩展**: UMD插件地址、用户ID关联

#### Session 表
- **会话信息**: 唯一ID、标题、创建/更新时间
- **关联**: 用户ID、Agent ID（外键约束）
- **元数据**: JSON格式的扩展信息存储

#### Message 表
- **A2U协议**: 基于A2U协议的消息存储结构
- **内容类型**: 支持文本、文件、数据等多种内容类型
- **分页支持**: 游标分页，支持时间顺序检索
- **扩展数据**: 支持元数据和扩展信息存储

## 🔧 配置管理

应用程序使用统一配置系统，支持Nacos配置中心实现动态配置管理。

### 配置方式

- **`.env`** - 本地基础配置文件
- **Nacos配置中心** - 远程动态配置（可选）

### 配置加载顺序

1. **本地配置加载**: 加载 `.env` 文件中的基础配置
2. **Nacos配置初始化**: 如果配置了Nacos，初始化配置中心连接
3. **远程配置拉取**: 从Nacos获取远程配置
4. **配置合并**: 远程配置覆盖本地配置（远程优先级更高）

### 必需的环境变量

- `NODE_ENV`: 环境类型 (production/development/test)
- `PORT`: 服务器端口
- `DB_HOST`: PostgreSQL 主机地址
- `DB_PORT`: PostgreSQL 端口
- `DB_NAME`: PostgreSQL 数据库名称
- `DB_USER`: PostgreSQL 用户名
- `DB_PASSWORD`: PostgreSQL 密码

### Nacos配置中心（可选）

如需使用Nacos配置中心，请配置以下环境变量：

```bash
# Nacos配置中心
nacos..enable=true
nacos.url=local:8848
nacos.namespace=yournamespace
nacos.rw.username=yourname
nacos.rw.password=yourpassword
```

### 配置特性

- ✅ **统一配置**: 不区分开发/生产环境，统一配置管理
- ✅ **动态配置**: 支持Nacos配置中心动态配置更新
- ✅ **配置回退**: Nacos不可用时自动使用本地配置
- ✅ **配置监听**: 支持配置变更监听和热更新
- ✅ **安全配置**: 敏感配置可存储在Nacos中

详细配置说明请参考：[Nacos配置文档](docs/NACOS_CONFIGURATION.md)

### CORS 配置

应用程序支持灵活的 CORS 配置，适用于开发和生产环境：

```bash
# 开发环境 - 允许所有来源
CORS_ORIGIN=*

# 生产环境 - 指定特定域名
CORS_ORIGIN=https://yourdomain.com,https://admin.yourdomain.com

# 高级选项（可选）
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Content-Type,Authorization,X-User-Id
CORS_EXPOSED_HEADERS=X-Total-Count,X-Rate-Limit-Remaining
CORS_MAX_AGE=86400
```

详细的 CORS 配置选项请参考 [CORS 配置指南](docs/CORS_CONFIGURATION.md)。

### 日志配置

应用程序支持灵活的日志配置：

```bash
# 开发环境 - 调试级别，可选文件日志
LOG_LEVEL=debug
LOG_FORMAT=dev
ENABLE_FILE_LOGGING=true

# 生产环境 - 信息级别，自动文件日志
LOG_LEVEL=info
LOG_FORMAT=combined
```

**日志文件位置**: `logs/combined.log` 和 `logs/error.log`

详细的日志配置请参考 [日志配置指南](docs/LOGGING_CONFIGURATION.md)。

### 本地覆盖配置

为本地开发创建 `.env.local` 覆盖配置：

```bash
cp .env.development .env.local
# 编辑 .env.local 设置你的本地配置
```

**注意**: `.env.local` 不应提交到版本控制系统。

## 🛡️ 安全特性

- **🔒 Helmet**: 安全头部设置
- **🌐 CORS**: 跨域资源共享配置
- **⚡ 速率限制**: 请求频率限制
- **✅ 输入验证**: Zod 模式验证
- **🛡️ SQL 注入防护**: Drizzle ORM 参数化查询
- **🔐 网关认证**: 通过 userId 头部的安全认证
- **🔑 密码哈希**: bcrypt 可配置轮数加密
- **📊 请求监控**: 实时请求监控和指标收集
- **🚨 错误跟踪**: 全局错误处理和分类

## 📊 健康检查

应用程序包含内置的健康检查端点：

- `GET /api/health` - 基础健康检查
- `GET /api/health/detailed` - 详细健康信息（包括数据库状态）

健康检查功能：
- ✅ 应用程序状态检查
- ✅ 数据库连接状态
- ✅ 系统资源监控
- ✅ 依赖服务状态

## 🔄 优雅关闭

应用程序在接收到 SIGINT 和 SIGTERM 信号时处理优雅关闭：

1. 🛑 停止接受新请求
2. 🔌 关闭数据库连接
3. ⏳ 等待正在进行的请求完成
4. 🚪 退出进程

## 🧪 测试框架

项目使用 Jest 测试框架，支持 TypeScript 和 BDD 测试：

- **🔬 单元测试**: 测试单个函数和模块
- **🎯 BDD测试**: 使用Gherkin语法的行为驱动开发测试
- **📊 覆盖率报告**: 自动生成测试覆盖率报告

### 测试特性
- ✅ TypeScript 支持
- ✅ Jest 测试框架
- ✅ BDD/Gherkin 特性文件支持
- ✅ 异步测试支持
- ✅ 测试覆盖率报告

## 📚 API 文档

- **Swagger UI**: 运行时访问 `/api-docs` 查看交互式 API 文档
- **OpenAPI 3.0**: 完整的 API 规范和示例，支持在线测试

## 🔧 核心特性

- **🚨 异常处理机制**: 全局错误处理、防御性编程和错误分类
- **📊 监控和健康管理**: 多级健康检查、实时监控和性能指标
- **⚙️ 进程管理**: 优雅关闭、进程监控和PM2集群管理
- **🔄 流式通信**: SSE流式聊天，支持流恢复和断线重连
- **🏗️ 模块化架构**: 基于TSyringe的依赖注入和模块化设计
- **📝 完整文档**: Swagger/OpenAPI 3.0交互式API文档

## 🚀 技术亮点

### 现代化架构
- ✅ **模块化设计**: 基于TSyringe依赖注入的清晰架构
- ✅ **高性能**: PostgreSQL连接池和查询优化
- ✅ **可扩展**: 模块化设计，易于扩展新功能
- ✅ **类型安全**: 完整的 TypeScript 类型系统

### 开发体验
- ✅ **热重载**: 开发时自动重启和更新
- ✅ **代码质量**: ESLint + Prettier 自动化代码规范
- ✅ **API文档**: Swagger UI 交互式文档
- ✅ **跨平台**: Windows、macOS、Linux 全平台支持

### 生产就绪
- ✅ **监控完备**: 健康检查、Langfuse LLM可观测性
- ✅ **安全加固**: Helmet安全头部、CORS配置、速率限制
- ✅ **部署灵活**: PM2进程管理和快速部署脚本
- ✅ **流式通信**: SSE实时聊天，支持better-sse流恢复


### 开发规范
- 遵循现有的代码风格和约定
- 添加适当的测试覆盖
- 更新相关文档
- 确保通过所有 CI 检查


**XUI App Server** - 现代化智能对话系统的后端服务 🚀
