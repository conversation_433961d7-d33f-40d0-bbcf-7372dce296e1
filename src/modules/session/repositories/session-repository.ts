/**
 * Session Repository
 * 
 * Data access layer for session-related database operations.
 */

import { injectable } from 'tsyringe';
import { desc, eq, and, sql, inArray } from 'drizzle-orm';
import type { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { session, type SessionInsert } from '@/infrastructure/database/schema/session';
import { agent } from '@/infrastructure/database/schema/agent';
import { message } from '@/infrastructure/database/schema/message';
import { databaseService } from '@/infrastructure/database';
import { logDatabaseOperation } from '@/infrastructure/logger';
import { measureTime } from '@/shared/utils/time';
import { generateUUID } from '@/shared/utils/crypto';
import type { SessionListQueryDto } from '../dto';
import { convertDatabaseError } from '@/shared/utils/errors';

export interface SessionEntity {
    id: string;
    title: string;
    userId: string;
    agentId: string;
    externalId: string;
    metadata: Record<string, unknown> | null;
    createdAt: Date;
    updatedAt: Date;
    taskId: string | null;
    taskState: 'submitted' | 'input-required' | 'auth-required' | null;
}
export interface CreateSessionEntity extends Omit<SessionInsert, 'id' | 'createdAt' | 'updatedAt'> { }
export interface UpdateSessionEntity extends Partial<Pick<SessionInsert, 'title'>> { }

export interface SessionListResult {
    sessions: SessionEntity[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
}

@injectable()
export class SessionRepository {
    private get db(): PostgresJsDatabase<Record<string, unknown>> {
        return databaseService.getDb();
    }

    /**
     * Find session by ID
     */
    async findById(id: string): Promise<SessionEntity | null> {
        try {
            const { result: sessions, duration } = await measureTime(async () => {
                return await this.db
                    .select()
                    .from(session)
                    .where(eq(session.id, id))
                    .limit(1);
            });

            logDatabaseOperation('SELECT', 'session', duration, {
                sessionId: id,
                found: sessions.length > 0,
            });

            return sessions[0] ?? null;
        } catch (error) {
            throw convertDatabaseError(error as Error, 'SELECT', 'session');
        }
    }

    /**
     * Create new session
     */
    async create(data: CreateSessionEntity): Promise<SessionEntity> {
        try {
            const sessionData: SessionInsert = {
                ...data,
                id: generateUUID(),
                createdAt: new Date(),
                updatedAt: new Date(),
            };

            const { result: createdSessions, duration } = await measureTime(async () => {
                return await this.db.insert(session).values(sessionData).returning();
            });

            const createdSession = createdSessions[0];
            if (!createdSession) {
                throw new Error('Failed to create session - no data returned');
            }

            logDatabaseOperation('INSERT', 'session', duration, {
                sessionId: createdSession.id,
                userId: data.userId,
                agentId: data.agentId,
            });

            return createdSession;
        } catch (error) {
            throw convertDatabaseError(error as Error, 'INSERT', 'session');
        }
    }

    /**
     * Update session
     */
    async update(id: string, data: UpdateSessionEntity): Promise<SessionEntity | null> {
        const updateData = {
            ...data,
            updatedAt: new Date(),
        };

        try {
            const { result: updatedSessions, duration } = await measureTime(async () => {
                return await this.db
                    .update(session)
                    .set(updateData)
                    .where(eq(session.id, id))
                    .returning();
            });

            logDatabaseOperation('UPDATE', 'session', duration, {
                sessionId: id,
                updated: updatedSessions.length > 0,
            });

            return updatedSessions[0] ?? null;
        } catch (error) {
            throw convertDatabaseError(error as Error, 'UPDATE', 'session');
        }
    }

    /**
     * Delete session
     */
    async delete(id: string): Promise<boolean> {
        try {
            const { result: deletedSessions, duration } = await measureTime(async () => {
                return await this.db
                    .delete(session)
                    .where(eq(session.id, id))
                    .returning();
            });

            logDatabaseOperation('DELETE', 'session', duration, {
                sessionId: id,
                deleted: deletedSessions.length > 0,
            });

            return deletedSessions.length > 0;
        } catch (error) {
            throw convertDatabaseError(error as Error, 'DELETE', 'session');
        }
    }

    /**
     * Find sessions by user with pagination and filtering
     */
    async findByUser(userId: string, query: SessionListQueryDto): Promise<SessionListResult> {
        const offset = (query.page - 1) * query.limit;

        // Build where conditions
        const conditions = [eq(session.userId, userId)];
        if (query.agentGroup !== undefined) {
            conditions.push(eq(agent.group, query.agentGroup));
        }

        try {
            // 使用窗口函数一次性获取数据和总数，避免两次查询
            const { result: results, duration } = await measureTime(async () => {
                return await this.db
                    .select({
                        session: session,
                        agent: agent,
                        totalCount: sql<number>`count(*) over()`.as('total_count')
                    })
                    .from(session)
                    .leftJoin(agent, eq(session.agentId, agent.id))
                    .where(and(...conditions))
                    .orderBy(desc(session.updatedAt))
                    .limit(query.limit)
                    .offset(offset);
            });

            const sessionEntities = results.map(item => item.session);
            const total = results.length > 0 ? Number(results[0]!.totalCount) : 0;

            logDatabaseOperation('SELECT', 'session', duration, {
                userId,
                total,
                page: query.page,
                limit: query.limit,
                agentGroup: query.agentGroup,
            });

            const totalPages = Math.ceil(total / query.limit);
            const hasNext = query.page < totalPages;
            const hasPrev = query.page > 1;

            return {
                sessions: sessionEntities,
                total,
                page: query.page,
                limit: query.limit,
                totalPages,
                hasNext,
                hasPrev,
            };
        } catch (error) {
            throw convertDatabaseError(error as Error, 'SELECT', 'session');
        }
    }


    /**
     * Find latest sessions by agent and user
     */
    async findLatestByAgentAndUser(
        agentId: string,
        externalId: string|undefined,
        userId: string,
        limit: number = 2
    ): Promise<SessionEntity[]> {
        try {
            const { result: sessions, duration } = await measureTime(async () => {
                const whereConditions = [
                    eq(session.agentId, agentId),
                    eq(session.userId, userId)
                ];

                if (externalId !== undefined) {
                    whereConditions.push(eq(session.externalId, externalId));
                }

                return await this.db
                    .select()
                    .from(session)
                    .where(and(...whereConditions))
                    .orderBy(desc(session.updatedAt))
                    .limit(limit);
            });

            logDatabaseOperation('SELECT', 'session', duration, {
                agentId,
                externalId,
                userId,
                limit,
                found: sessions.length,
                operation: 'findLatestByAgentAndUser',
            });

            return sessions;
        } catch (error) {
            throw convertDatabaseError(error as Error, 'SELECT', 'session');
        }
    }

    /**
     * 批量检查多个agent是否有会话（性能优化版本）
     * 使用单次IN查询替代多次单独查询
     */
    async batchCheckAgentsHaveSessions(agentIds: string[], userId: string): Promise<Map<string, boolean>> {
        if (agentIds.length === 0) {
            return new Map();
        }

        try {
            const { result: sessions, duration } = await measureTime(async () => {
                return await this.db
                    .select({
                        agentId: session.agentId,
                    })
                    .from(session)
                    .where(and(
                        inArray(session.agentId, agentIds), // 使用inArray进行批量查询
                        eq(session.userId, userId)
                    ))
                    .groupBy(session.agentId); // 按agentId分组，每个agent只返回一条记录
            });

            logDatabaseOperation('SELECT', 'session', duration, {
                agentIds: agentIds.length,
                userId,
                found: sessions.length,
                operation: 'batchCheckAgentsHaveSessions',
            });

            // 构建结果映射
            const statusMap = new Map<string, boolean>();

            // 初始化所有agent为false
            agentIds.forEach(agentId => statusMap.set(agentId, false));

            // 设置有会话的agent为true
            sessions.forEach(sessionResult => {
                statusMap.set(sessionResult.agentId, true);
            });

            return statusMap;
        } catch (error) {
            throw convertDatabaseError(error as Error, 'SELECT', 'session');
        }
    }

    /**
     * Find session with full agent info for chat validation (优化版本)
     * 使用 JOIN 一次性获取会话和完整的 Agent 信息
     */
    async findByIdWithFullAgentInfo(sessionId: string, userId: string, agentId: string): Promise<{
        session: SessionEntity;
        agent: { cardUrl: string | null; name: string; id: string };
    } | null> {
        try {
            const { result: results, duration } = await measureTime(async () => {
                return await this.db
                    .select({
                        session: session,
                        agent: {
                            id: agent.id,
                            name: agent.name,
                            cardUrl: agent.cardUrl,
                        },
                    })
                    .from(session)
                    .innerJoin(agent, eq(session.agentId, agent.id))
                    .where(and(
                        eq(session.id, sessionId),
                        eq(session.userId, userId),
                        eq(session.agentId, agentId)
                    ))
                    .limit(1);
            });

            logDatabaseOperation('SELECT', 'session', duration, {
                sessionId,
                userId,
                agentId,
                found: results.length > 0,
                operation: 'findByIdWithFullAgentInfo_optimized',
            });

            return results[0] ?? null;
        } catch (error) {
            throw convertDatabaseError(error as Error, 'SELECT', 'session');
        }
    }

    /**
     * Delete all messages for a session
     */
    async deleteSessionMessages(sessionId: string): Promise<number> {
        try {
            const { result: deletedMessages, duration } = await measureTime(async () => {
                return await this.db
                    .delete(message)
                    .where(eq(message.sessionId, sessionId))
                    .returning();
            });

            logDatabaseOperation('DELETE', 'message', duration, {
                sessionId,
                deletedCount: deletedMessages.length,
            });

            return deletedMessages.length;
        } catch (error) {
            throw convertDatabaseError(error as Error, 'DELETE', 'message');
        }
    }

    /**
     * add session taskid and state
     */
    async addTaskIdAndState(
        sessionId: string, 
        taskId: string, 
        taskState: 'submitted' | 'input-required' | 'auth-required'
    ): Promise<SessionEntity | null> {
        try {
            const { result: updatedSessions, duration } = await measureTime(async () => {
                return await this.db
                    .update(session)
                    .set({ taskId, taskState })
                    .where(eq(session.id, sessionId))
                    .returning();
            });

            logDatabaseOperation('UPDATE', 'session', duration, {
                sessionId,
                updated: updatedSessions.length > 0,
            });

            return updatedSessions[0] ?? null;
        } catch (error) {
            throw convertDatabaseError(error as Error, 'UPDATE', 'session');
        }
    }

    /**
     * clear session taskId and taskState
     */
    async clearTaskIdAndState(sessionId: string): Promise<SessionEntity | null> {
        try {
            const { result: updatedSessions, duration } = await measureTime(async () => {
                return await this.db
                    .update(session)
                    .set({ taskId: null, taskState: null })
                    .where(eq(session.id, sessionId))
                    .returning();
            });

            logDatabaseOperation('UPDATE', 'session', duration, {
                sessionId,
                updated: updatedSessions.length > 0,
            });

            return updatedSessions[0] ?? null;
        } catch (error) {
            throw convertDatabaseError(error as Error, 'UPDATE', 'session');
        }
    }
}