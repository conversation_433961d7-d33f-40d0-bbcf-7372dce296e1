/**
 * Session Data Transfer Objects
 * 
 * DTOs for session-related API operations with proper validation and typing.
 */

import { z } from 'zod/v4';
import {
    MessageContentTypeSchema,
    MessageRoleSchema,
} from '@/modules/message/dto/message-dto';

// Base Session DTO
export const SessionDtoSchema = z.object({
    id: z.uuid(),
    title: z.string().min(1).max(200),
    userId: z.uuid(),
    agentId: z.uuid(),
    externalId: z.string(),
    metadata: z.record(z.string(), z.unknown()).nullable(),
    createdAt: z.date(),
    updatedAt: z.date(),
});

export type SessionDto = z.infer<typeof SessionDtoSchema>;

// Create Session Request DTO
export const CreateSessionRequestDtoSchema = z.object({
    agentId: z.uuid({ message: 'Invalid agent ID format' }),
    externalId: z.string().min(1, 'externalId At least one content item is required').optional()
});

export type CreateSessionRequestDto = z.infer<typeof CreateSessionRequestDtoSchema>;

// Update Session Request DTO
export const UpdateSessionRequestDtoSchema = z.object({
    title: z.string().min(1).max(200),
});

export type UpdateSessionRequestDto = z.infer<typeof UpdateSessionRequestDtoSchema>;

// Session List Query DTO
export const SessionListQueryDtoSchema = z.object({
    page: z.coerce.number().min(1).default(1),
    limit: z.coerce.number().min(1).max(100).default(10),
    agentGroup: z.coerce.number().min(1).max(3).optional(),
});

export type SessionListQueryDto = z.infer<typeof SessionListQueryDtoSchema>;


// Session Response DTO (for API responses)
export const SessionResponseDtoSchema = z.object({
    id: z.string(),
    title: z.string(),
    userId: z.string(),
    agentId: z.string(),
    externalId: z.string(),
    metadata: z.record(z.string(), z.unknown()).nullable(),
    createdAt: z.string(),
    updatedAt: z.string(),
    taskId: z.string().nullable(),
    taskState: z.enum(['submitted', 'input-required', 'auth-required']).nullable(),
});

export type SessionResponseDto = z.infer<typeof SessionResponseDtoSchema>;

// Agent info for session response
export const SessionAgentInfoSchema = z.object({
    id: z.string(),
    name: z.string(),
    avatar: z.string(),
    type: z.number(),
    typeDescription: z.string(),
    target: z.number(),
    targetDescription: z.string(),
    status: z.number(),
    statusDescription: z.string(),
    group: z.number(),
    groupDescription: z.string(),
    isSingleSession: z.boolean(),
    sessionModeDescription: z.string(),
    umdUrl: z.string().nullable(),
    extraInfo: z.unknown().optional(),
});

export type SessionAgentInfo = z.infer<typeof SessionAgentInfoSchema>;

// Session Response DTO with Agent Info (for session list)
export const SessionWithAgentResponseDtoSchema = z.object({
    id: z.string(),
    title: z.string(),
    userId: z.string(),
    agentId: z.string(),
    externalId: z.string(),
    agent: SessionAgentInfoSchema,
    metadata: z.record(z.string(), z.unknown()).nullable(),
    createdAt: z.string(),
    updatedAt: z.string(),
});

export type SessionWithAgentResponseDto = z.infer<typeof SessionWithAgentResponseDtoSchema>;

// Chat Request DTO
export const ChatRequestDtoSchema = z.object({
    agentId: z.uuid({ message: 'Invalid agent ID format' }),
    message: z.object({
        id: z.string().min(1, 'Message ID is required'),
        role: MessageRoleSchema,
        content: z
            .array(
                z.object({
                    type: MessageContentTypeSchema,
                    text: z.string().optional(),
                    file: z
                        .object({
                            bytes: z.string().optional(),
                            uri: z.string().optional()
                        })
                        .optional(),
                    data: z
                        .union([z.record(z.string(), z.unknown()), z.string()])
                        .optional(),
                    extendedData: z.record(z.string(), z.unknown()).optional(),
                })
            )
            .min(1, 'At least one content item is required'),
        sender: z.object({
                id: z.string().min(1, 'Sender ID is required'),
                name: z.string().optional(),
                avatar: z.string().optional(),
                type: z.string().optional(),
            }).nullable(),
        extendedData: z.record(z.string(), z.unknown()).nullable().optional(),
    }).optional(),
});

export type ChatRequestDto = z.infer<typeof ChatRequestDtoSchema>; 