/**
 * Session Validators
 * 
 * Express middleware for validating session-related requests.
 * Provides reusable validation functions for session operations.
 */

import { commonSchemas } from '@/shared/utils/validator-helpers';
import {
    createParamsValidator,
    createBodyValidator,
    createQueryValidator
} from '@/shared/utils/validator-factory';
import {
    CreateSessionRequestDtoSchema,
    UpdateSessionRequestDtoSchema,
    SessionListQueryDtoSchema,
    ChatRequestDtoSchema,
} from '../dto/session-dto';
import { z } from 'zod/v4';
import type { RequestHandler } from 'express';

/**
 * Pre-built validators using the generic factory functions
 */
export const validateSessionId: RequestHandler = createParamsValidator(
    z.object({ sessionId: commonSchemas.uuid }),
    'Invalid session ID'
);
export const validateAgentId: RequestHandler = createParamsValidator(
    z.object({ agentId: commonSchemas.uuid }),
    'Invalid agent ID'
);

export const validateCancelChatRequest: RequestHandler = createBodyValidator(
    z.object({ agentId: commonSchemas.uuid }),
    'Invalid agent ID'
);

export const validateCreateSessionRequest: RequestHandler = createQueryValidator(
    CreateSessionRequestDtoSchema as unknown as z.ZodType, 'create session request'
);
export const validateUpdateSessionRequest: RequestHandler = createBodyValidator(
    UpdateSessionRequestDtoSchema as unknown as z.ZodType, 'update session request'
);
export const validateSessionListQuery: RequestHandler = createQueryValidator(
    SessionListQueryDtoSchema as unknown as z.ZodType, 'session list query'
);

export const validateChatRequest: RequestHandler = createBodyValidator(
    ChatRequestDtoSchema as unknown as z.ZodType, 'chat request'
);

/**
 * Combined validator arrays for different operations
 */
export const validateCreateSession: RequestHandler[] = [
    validateCreateSessionRequest,
];

export const validateGetUserSessions: RequestHandler[] = [
    validateSessionListQuery,
];

export const validateGetSession: RequestHandler[] = [
    validateSessionId,
];

export const validateUpdateSession: RequestHandler[] = [
    validateSessionId,
    validateUpdateSessionRequest,
];

export const validateDeleteSession: RequestHandler[] = [
    validateSessionId,
];

export const validateChat: RequestHandler[] = [
    validateSessionId,
    validateChatRequest,
];

export const validateCancelChat: RequestHandler[] = [
    validateSessionId,
    validateCancelChatRequest,
];