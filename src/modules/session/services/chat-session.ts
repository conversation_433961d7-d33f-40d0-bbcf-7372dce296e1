import { randomUUID } from 'crypto';
import { CreateMessageRequestDto } from '@/modules/message/dto/message-dto';
import type { MessageContent as DBA2UMessageContent } from '@/infrastructure/database/schema/message';

/**
 * 聊天会话状态类
 * 每个聊天请求创建一个独立的状态实例，避免并发请求时的状态混乱
 */
export class ChatSession {
    public messageId: string = '';
    public partIndex: number = -1;
    public partType: string = 'text';
    public dbId: string = '';
    public agentMessage: CreateMessageRequestDto | undefined = undefined;
    public a2uMessageContent: DBA2UMessageContent | undefined = undefined;
    public readonly tags: Set<string> = new Set();

    constructor(
        public readonly sessionId: string,
        public readonly userId: string,
        public readonly agentId: string,
    ) {}

    /**
     * 初始化会话状态
     */
    public initialize(): void {
        this.dbId = '';
        this.tags.clear();
        this.messageId = '';
        this.partIndex = -1;
        this.a2uMessageContent = undefined;
        this.agentMessage = {
            id: randomUUID(),
            sessionId: this.sessionId,
            role: 'assistant',
            content: [],
            sender: {
                id: this.agentId,
            },
        };
    }

    /**
     * 更新消息内容
     */
    public updateMessageContent(type: string, value: unknown): void {
        if (!this.a2uMessageContent) {
            return;
        }

        // 根据 a2UMessageContent 的实际类型来处理内容更新
        if (this.a2uMessageContent.type === 'text') {
            // text 类型：所有内容都作为文本拼接
            const currentText = this.a2uMessageContent.text ?? '';
            this.a2uMessageContent.text = currentText + String(value);
        } else if (this.a2uMessageContent.type === 'file') {
            // file 类型：根据 contentType 处理
            if (value !== null && typeof value === 'object') {
                this.a2uMessageContent.file = value as {
                    bytes?: string;
                    uri?: string;
                    metadata?: Record<string, unknown>
                };
            }
        } else {
            if (type === 'data') {
                this.a2uMessageContent.data = value as Record<string, unknown> | string;
            } else {
                // 对于非data类型的更新，将值作为字符串添加到数组
                const currentData = this.a2uMessageContent.data ?? '';
                this.a2uMessageContent.data = currentData + String(value);
            }
        }
    }
}
