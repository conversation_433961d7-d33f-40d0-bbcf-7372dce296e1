/**
 * Session Mapper
 * 
 * Maps session entities to DTOs for API responses.
 */

import { injectable } from 'tsyringe';
import type { SessionEntity } from '../repositories';
import type { SessionResponseDto, SessionWithAgentResponseDto, SessionAgentInfo } from '../dto';
import { getDateTime } from '@/shared/utils';
@injectable()
export class SessionMapper {
    /**
     * Maps a session entity to a response DTO.
     * @param session The session entity.
     * @returns The session response DTO.
     */
    toDto(session: SessionEntity): SessionResponseDto {
        return {
            id: session.id,
            title: session.title,
            userId: session.userId,
            agentId: session.agentId,
            externalId: session.externalId,
            createdAt: getDateTime(session.createdAt),
            updatedAt: getDateTime(session.updatedAt),
            metadata: session.metadata,
            taskId: session.taskId,
            taskState: session.taskState,
        };
    }

    /**
     * Maps an array of session entities to an array of response DTOs.
     * @param sessions The array of session entities.
     * @returns An array of session response DTOs.
     */
    toDtoList(sessions: SessionEntity[]): SessionResponseDto[] {
        return sessions.map(session => this.toDto(session));
    }

    /**
     * Maps a session entity with agent info to a response DTO with agent.
     * @param session The session entity.
     * @param agent The agent info.
     * @returns The session response DTO with agent info.
     */
    toDtoWithAgent(session: SessionEntity, agent: SessionAgentInfo): SessionWithAgentResponseDto {
        return {
            id: session.id,
            title: session.title,
            userId: session.userId,
            agentId: session.agentId,
            externalId: session.externalId,
            agent,
            createdAt: getDateTime(session.createdAt),
            updatedAt: getDateTime(session.updatedAt),
            metadata: session.metadata
        };
    }

    /**
     * Maps an array of session entities with agent info to an array of response DTOs with agent.
     * @param sessionsWithAgents Array of tuples containing session and agent info.
     * @returns An array of session response DTOs with agent info.
     */
    toDtoListWithAgent(
        sessionsWithAgents: Array<{ session: SessionEntity; agent: SessionAgentInfo }>
    ): SessionWithAgentResponseDto[] {
        return sessionsWithAgents.map(({ session, agent }) => this.toDtoWithAgent(session, agent));
    }
} 