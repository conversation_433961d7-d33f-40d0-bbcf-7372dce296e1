/**
 * Session Service
 * 
 * Business logic layer for session operations.
 * Handles session creation, retrieval, update, and deletion with proper validation.
 */

import { injectable, inject } from 'tsyringe';
import { SessionRepository, type SessionEntity } from '../repositories';
import { SessionMapper } from './session-mapper';
import { ValidationError, ResourceNotFoundError } from '@/shared/utils/errors';
import { TYPES } from '@/shared/constants';
import type {
    SessionListQueryDto,
    CreateSessionRequestDto,
    UpdateSessionRequestDto,
    SessionResponseDto,
    SessionWithAgentResponseDto,
    SessionAgentInfo,
} from '../dto';
import { AgentRepository } from '@/modules/agent/repositories';
import {
    getAgentTypeDescription,
    getAgentTargetDescription,
    getAgentStatusDescription,
    getAgentGroupDescription,
    getAgentSessionModeDescription
} from '@/modules/agent/dto/agent-dto';

export interface PaginatedSessionResponse {
    sessions: SessionWithAgentResponseDto[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
}

@injectable()
export class SessionService {
    constructor(
        @inject(TYPES.SessionRepository) private readonly sessionRepository: SessionRepository,
        @inject(TYPES.AgentRepository) private readonly agentRepository: AgentRepository,
        @inject(TYPES.SessionMapper) private readonly sessionMapper: SessionMapper
    ) { }

    /**
     * Get user sessions with pagination and filtering
     */
    async getUserSessions(userId: string, query: SessionListQueryDto): Promise<PaginatedSessionResponse> {
        const result = await this.sessionRepository.findByUser(userId, query);

        // 获取所有唯一的agentId
        const agentIds = [...new Set(result.sessions.map(session => session.agentId))];

        // 批量获取agent信息
        const agents = await this.agentRepository.findByIds(agentIds);

        // 创建agentId到agent的映射
        const agentMap = new Map(
            agents
                .map(agent => [agent!.id, agent!])
        );

        // 为每个session创建SessionAgentInfo并组合数据
        const sessionsWithAgents = result.sessions
            .map(session => {
                const agent = agentMap.get(session.agentId);
                if (!agent) {
                    return null;
                }

                const agentInfo: SessionAgentInfo = {
                    id: agent.id,
                    name: agent.name,
                    avatar: agent.avatar,
                    type: agent.type,
                    typeDescription: getAgentTypeDescription(agent.type),
                    target: agent.target,
                    targetDescription: getAgentTargetDescription(agent.target),
                    status: agent.status,
                    statusDescription: getAgentStatusDescription(agent.status),
                    group: agent.group,
                    groupDescription: getAgentGroupDescription(agent.group),
                    isSingleSession: agent.isSingleSession,
                    sessionModeDescription: getAgentSessionModeDescription(agent.isSingleSession),
                    umdUrl: agent.umdUrl,
                    extraInfo: agent.extraInfo,
                };

                return { session, agent: agentInfo };
            })
            .filter(item => item !== null) as Array<{ session: SessionEntity; agent: SessionAgentInfo }>;

        return {
            ...result,
            sessions: this.sessionMapper.toDtoListWithAgent(sessionsWithAgents),
        };
    }

    /**
     * Get session by ID with access control
     */
    async getSessionById(sessionId: string, userId: string): Promise<SessionResponseDto> {
        const session = await this.sessionRepository.findById(sessionId);

        if (!session || session.userId !== userId) {
            throw new ResourceNotFoundError('Session', 'Session not found');
        }

        return this.sessionMapper.toDto(session);
    }

    /**
     * Create new session
     */
    async createSession(userId: string, query: CreateSessionRequestDto): Promise<SessionResponseDto> {
        const { agentId, externalId } = query;
        const agent = await this.agentRepository.findById(agentId);
        if (!agent) {
            throw new ResourceNotFoundError('Agent not found');
        }

        // Always check for existing sessions when externalId is provided to prevent duplicate key violations
        if (externalId !== undefined && externalId.trim() !== '') {
            const existingSessions = await this.sessionRepository.findLatestByAgentAndUser(agentId, externalId, userId);
            if (existingSessions.length > 0) {
                return this.sessionMapper.toDto(existingSessions[0] as SessionEntity);
            }
        }

        // For single session agents, check for any existing sessions
        if (agent.isSingleSession === true) {
            const sessions = await this.sessionRepository.findLatestByAgentAndUser(agentId, externalId, userId);
            if (sessions.length > 0) {
                return this.sessionMapper.toDto(sessions[0] as SessionEntity);
            }
        }

        const title = `与 ${agent.name} 的对话`;
        const createdSession = await this.sessionRepository.create({
            agentId,
            externalId,
            userId,
            title
        });

        return this.sessionMapper.toDto(createdSession);
    }

    /**
     * Update session with access control
     */
    async updateSession(sessionId: string, userId: string, data: UpdateSessionRequestDto): Promise<SessionResponseDto> {
        const existingSession = await this.sessionRepository.findById(sessionId);
        if (!existingSession || existingSession.userId !== userId) {
            throw new ResourceNotFoundError('Session not found');
        }

        const updatedSession = await this.sessionRepository.update(sessionId, data);

        if (!updatedSession) {
            throw new ResourceNotFoundError('Session not found after update');
        }

        return this.sessionMapper.toDto(updatedSession);
    }

    /**
     * Delete session with access control
     */
    async deleteSession(sessionId: string, userId: string): Promise<{ id: string }> {
        if (!sessionId || !userId) {
            throw new ValidationError('Session ID and User ID are required');
        }

        const session = await this.sessionRepository.findById(sessionId);
        if (!session || session.userId !== userId) {
            throw new ResourceNotFoundError('Session not found');
        }

        await this.sessionRepository.deleteSessionMessages(sessionId);
        const deleted = await this.sessionRepository.delete(sessionId);

        if (!deleted) {
            throw new ResourceNotFoundError('Session not found for deletion');
        }

        return { id: sessionId };
    }

    async addTaskIdAndState(
        sessionId: string,
        taskId: string,
        taskState: 'submitted' | 'input-required' | 'auth-required'
    ): Promise<SessionEntity | null> {
        return await this.sessionRepository.addTaskIdAndState(sessionId, taskId, taskState);
    }

    async clearTaskIdAndState(sessionId: string): Promise<SessionEntity | null> {
        return await this.sessionRepository.clearTaskIdAndState(sessionId);
    }
} 