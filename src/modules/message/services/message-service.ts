/**
 * Message Service
 * 
 * Business logic for message-related operations.
 */

import { inject, injectable } from 'tsyringe';
import { TYPES } from '@/shared/constants';
import { SmartErrorLogger } from '@/infrastructure/logger/smart-error-logger';
import { ResourceNotFoundError } from '@/shared/utils/errors';
import { getDateTime } from '@/shared/utils/time';
import type { 
    MessageRepository, 
    MessageEntity, 
    CreateMessageEntity, 
    UpdateMessageEntity 
} from '../repositories/message-repository';
import type { MessageMapper } from './message-mapper';
import type { 
    MessageResponseDto, 
    MessageListResponseDto, 
    MessageListQueryDto,
    CreateMessageRequestDto,
    UpdateMessageRequestDto,
} from '../dto';

@injectable()
export class MessageService {
    constructor(
        @inject(TYPES.MessageRepository) private readonly messageRepository: MessageRepository,
        @inject(TYPES.MessageMapper) private readonly messageMapper: MessageMapper
    ) {}

    /**
     * Get message by ID with access control (优化版本)
     * 使用单次查询同时获取消息和验证权限
     */
    async getMessageById(dbId: string, userId: string): Promise<MessageResponseDto> {
        const message = await this.messageRepository.findByIdWithAccess(dbId, userId);

        if (message === null) {
            // 使用SmartErrorLogger记录业务异常
            const logContext = { requestId: 'unknown', userId, method: 'getMessageById', url: '/message' };
            SmartErrorLogger.logError(
                new ResourceNotFoundError('Message', 'Message not found'),
                logContext,
                'service'
            );
            throw new ResourceNotFoundError('Message', 'Message not found');
        }

        return this.messageMapper.toDto(message);
    }

    /**
     * Get session messages with pagination and filtering
     */
    async getSessionMessages(
        userId: string, 
        sessionId: string,
        query: MessageListQueryDto
    ): Promise<MessageListResponseDto> {
        const hasAccess = await this.messageRepository.verifySessionAccess(sessionId, userId);
        if (!hasAccess) {
            throw new ResourceNotFoundError('Session not found');
        }

        const queryWithDefaults = { ...query };
        if (queryWithDefaults.startTime && !queryWithDefaults.endTime) {
            queryWithDefaults.endTime = new Date();
        }

        const result = await this.messageRepository.findBySession(sessionId, queryWithDefaults);

        return {
            messages: result.messages.map(message => this.messageMapper.toDto(message)),
            hasNextPage: result.hasNextPage,
            nextCursor: result.nextCursor ?? undefined,
            limit: result.limit,
        };
    }

    /**
     * Delete message
     */
    async deleteMessage(dbId: string, userId: string): Promise<{ id: string }> {
        const message = await this.messageRepository.findById(dbId);
        if (!message) {
            throw new ResourceNotFoundError('Message not found');
        }
        
        const hasAccess = await this.messageRepository.verifySessionAccess(message.sessionId, userId);
        if (!hasAccess) {
            throw new ResourceNotFoundError('Message not found');
        }
        
        await this.messageRepository.delete(dbId);

        return { id: dbId };
    }

    async createMessage(message: CreateMessageRequestDto): Promise<MessageEntity> {
        const createdMessage =  await this.messageRepository.create(message as CreateMessageEntity);
        return createdMessage;
    }

    async updateMessage(dbId: string, userId: string, message: UpdateMessageRequestDto): Promise<MessageResponseDto> {
        // 优化：使用条件更新一次性完成权限验证和更新
        const updatedMessage = await this.messageRepository.updateWithAccess({
            ...message,
            dbId,
        } as UpdateMessageEntity, userId);

        if (!updatedMessage) {
            throw new ResourceNotFoundError('Message not found or access denied');
        }

        return this.messageMapper.toDto(updatedMessage);
    }

    async hasMessagesBySessionId(sessionId: string): Promise<boolean> {
        return await this.messageRepository.hasMessageBySession(sessionId);
    }

    /**
     * Export all messages for a session as CSV (without user filtering)
     */
    async exportSessionMessagesAsCSV(sessionId: string): Promise<string> {
        // Get all messages for the session without user filtering
        const messages = await this.messageRepository.findAllBySession(sessionId);

        if (messages.length === 0) {
            throw new ResourceNotFoundError('Session', 'No messages found for this session');
        }

        // Convert messages to CSV format
        return this.convertMessagesToCSV(messages);
    }

    /**
     * Convert messages to CSV format
     */
    private convertMessagesToCSV(messages: MessageEntity[]): string {
        // CSV headers
        const headers = [
            'db_id',
            'id',
            'role',
            'content',
            'sender',
            'extended_data',
            'created_at',
            'updated_at',
            'session_id',
            'user_id',
            'tags'
        ];

        // Convert messages to CSV rows
        const rows = messages.map(message => {
            return [
                this.escapeCsvValue(message.dbId),
                this.escapeCsvValue(message.id),
                this.escapeCsvValue(message.role),
                this.escapeCsvValue(JSON.stringify(message.content)),
                this.escapeCsvValue(message.sender ? JSON.stringify(message.sender) : '(null)'),
                this.escapeCsvValue(message.extendedData ? JSON.stringify(message.extendedData) : '(null)'),
                this.escapeCsvValue(getDateTime(message.createdAt)),
                this.escapeCsvValue(getDateTime(message.updatedAt)),
                this.escapeCsvValue(message.sessionId),
                this.escapeCsvValue(message.userId ?? '(null)'),
                this.escapeCsvValue('(null)') // tags field - not available in current schema
            ];
        });

        // Combine headers and rows
        const csvContent = [headers.join(','), ...rows.map(row => row.join(','))].join('\n');

        return csvContent;
    }

    /**
     * Escape CSV values to handle commas, quotes, and newlines
     */
    private escapeCsvValue(value: string | null | undefined): string {
        if (value === null || value === undefined) {
            return '(null)';
        }

        const stringValue = String(value);

        // If the value contains comma, quote, or newline, wrap it in quotes and escape internal quotes
        if (
          stringValue.includes(',') ||
          stringValue.includes('"') ||
          stringValue.includes('\n') ||
          stringValue.includes('\r')
        ) {
            return `"${stringValue.replace(/"/g, '""')}"`;
        }

        return stringValue;
    }
}