/**
 * Message Repository
 * 
 * Data access layer for message-related database operations.
 * Simplified to match actual database schema.
 */

import { injectable } from 'tsyringe';
import { desc, lt, eq, and, gte, lte, sql } from 'drizzle-orm';
import type { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { message, type MessageInsert } from '@/infrastructure/database/schema/message';
import { session } from '@/infrastructure/database/schema/session';
import { databaseService } from '@/infrastructure/database';
import { logDatabaseOperation } from '@/infrastructure/logger';
import { measureTime } from '@/shared/utils/time';
import { generateUUID } from '@/shared/utils/crypto';
import type { MessageListQueryDto } from '../dto';
import { convertDatabaseError } from '@/shared/utils';

export interface MessageEntity {
    dbId: string;
    id: string;
    sessionId: string;
    role: 'user' | 'assistant';
    content: Array<{
        type: 'text' | 'file' | 'data';
        text?: string;
        file?: {
            bytes?: string;
            uri?: string;
            metadata?: Record<string, unknown>;
        };
        data?: Record<string, unknown> | string;
        metadata?: Record<string, unknown>;
    }>;
    sender: {
        id: string;
        name?: string;
        avatar?: string;
        type?: string;
        [key: string]: unknown;
    } | null;
    extendedData: Record<string, unknown> | null;
    createdAt: Date;
    updatedAt: Date;
    userId: string | null;
}

export interface CreateMessageEntity extends Omit<MessageInsert, 'dbId' | 'createdAt' | 'updatedAt' | 'content'> {
    content: Array<{
        type: 'text' | 'file' | 'data';
        text?: string;
        file?: {
            bytes?: string;
            uri?: string;
            metadata?: Record<string, unknown>;
        };
        data?: Record<string, unknown> | string;
        metadata?: Record<string, unknown>;
    }>;
}

export interface UpdateMessageEntity extends Omit<MessageInsert, 'createdAt' | 'content'> {
    content: Array<{
        type: 'text' | 'file' | 'data';
        text?: string;
        file?: {
            bytes?: string;
            uri?: string;
            metadata?: Record<string, unknown>;
        };
        data?: Record<string, unknown> | string;
        metadata?: Record<string, unknown>;
    }>;
}

export interface MessageListResult {
    messages: MessageEntity[];
    limit: number;
    hasNextPage: boolean;
    nextCursor?: string | null;
    cursor?: string | null;
}


@injectable()
export class MessageRepository {
    private get db(): PostgresJsDatabase<Record<string, unknown>> {
        return databaseService.getDb();
    }

    /**
     * Find message by ID
     */
    async findById(id: string): Promise<MessageEntity | null> {
        try {
            const { result: messages, duration } = await measureTime(async () => {
                return await this.db
                    .select()
                    .from(message)
                    .where(eq(message.dbId, id))
                    .limit(1);
            });

            logDatabaseOperation('SELECT', 'message', duration, {
                messageDbId: id,
                found: messages.length > 0,
            });

            return messages[0] ?? null;
        } catch (error) {
            throw convertDatabaseError(error as Error, 'SELECT', 'message');
        }
    }

    /**
     * Find message by ID with access control (优化版本)
     * 直接在 message 表上验证用户权限，无需 JOIN
     */
    async findByIdWithAccess(id: string, userId: string): Promise<MessageEntity | null> {
        try {
            const { result: messages, duration } = await measureTime(async () => {
                return await this.db
                    .select()
                    .from(message)
                    .where(and(
                        eq(message.dbId, id),
                        eq(message.userId, userId)
                    ))
                    .limit(1);
            });

            logDatabaseOperation('SELECT', 'message', duration, {
                messageDbId: id,
                userId,
                found: messages.length > 0,
                operation: 'findByIdWithAccess_optimized',
            });

            return messages[0] ?? null;
        } catch (error) {
            throw convertDatabaseError(error as Error, 'SELECT', 'message');
        }
    }

    /**
     * Create new message and update session timestamp
     */
    async create(data: CreateMessageEntity): Promise<MessageEntity> {
        const messageData: MessageInsert = {
            ...data,
            id: data.id || generateUUID(),
            createdAt: new Date(),
            updatedAt: new Date(),
        };
        try {
            const { result: createdMessage, duration } = await measureTime(async () => {
                return await this.db.transaction(async (tx) => {
                    const createdMessages = await tx.insert(message).values(messageData).returning();
                    const newMsg = createdMessages[0];

                    if (!newMsg) {
                        tx.rollback();
                        throw new Error('Failed to create message - no data returned during transaction');
                    }

                    await tx
                        .update(session)
                        .set({ updatedAt: messageData.createdAt })
                        .where(eq(session.id, data.sessionId));

                    return newMsg;
                });
            });

            logDatabaseOperation('TRANSACTION', 'message/session', duration, {
                operation: 'createMessageAndUpdateSession',
                messageId: createdMessage.id,
                sessionId: data.sessionId,
                role: data.role,
            });

            return createdMessage;
        } catch (error) {
            throw convertDatabaseError(error as Error, 'INSERT', 'message');
        }
    }

    /**
     * Find messages by session with pagination and filtering
     * 优化：使用 updatedAt
     */
    async findBySession(sessionId: string, query: MessageListQueryDto): Promise<MessageListResult> {
        const { cursor, limit, tags, startTime, endTime } = query;

        const orderDirection = desc;
        const orderColumn = message.updatedAt;

        // Build where conditions
        const conditions = [eq(message.sessionId, sessionId)];

        if (cursor !== undefined && cursor.trim() !== '') {
            // 优化：使用子查询获取游标消息的时间戳，避免额外的 findById 调用
            const cursorSubquery = this.db
                .select({ updatedAt: message.updatedAt })
                .from(message)
                .where(eq(message.dbId, cursor))
                .limit(1);

            conditions.push(
                lt(message.updatedAt,
                    sql`(${cursorSubquery})`
                )
            );
        }

        if (startTime) {
            conditions.push(gte(message.updatedAt, startTime));
        }

        if (endTime) {
            conditions.push(lte(message.createdAt, endTime));
        }

        if (tags && tags.length > 0) {
            const tagsArray = Array.isArray(tags) ? tags : [String(tags)];
            conditions.push(sql`${message.tags} && ${sql.param(tagsArray)}`);
        }

        try {
            // Get messages with pagination
            const { result: messages, duration: selectDuration } = await measureTime(async () => {
                return await this.db
                    .select()
                    .from(message)
                    .where(and(...conditions))
                    .orderBy(orderDirection(orderColumn))
                    .limit(limit + 1); // +1 to check if there is more data
            });


            const hasNextPage = messages.length > limit;
            const resultMessages = hasNextPage ? messages.slice(0, limit) : messages;

            // 获取下一页的cursor
            const nextCursor =
                hasNextPage && messages[limit - 1] ? messages[limit - 1]!.dbId : null;

            logDatabaseOperation('SELECT', 'message', selectDuration, {
                sessionId: sessionId,
                cursor: cursor,
                limit: limit,
                hasNextPage,
                nextCursor: nextCursor ?? null,
                count: resultMessages.length,
            });
            return {
                messages: resultMessages.reverse(),
                limit: limit,
                hasNextPage,
                nextCursor,
            };
        } catch (error) {
            throw convertDatabaseError(error as Error, 'SELECT', 'message');
        }
    }

    /**
     * Delete message by ID
     */
    async delete(id: string): Promise<boolean> {
        try {
            const { result: deletedMessages, duration } = await measureTime(async () => {
                return await this.db
                    .delete(message)
                    .where(eq(message.dbId, id))
                    .returning();
            });

            logDatabaseOperation('DELETE', 'message', duration, {
                messageDbId: id,
                deleted: deletedMessages.length > 0,
            });

            return deletedMessages.length > 0;
        } catch (error) {
            throw convertDatabaseError(error as Error, 'DELETE', 'message');
        }
    }

    /**
     * Verify session exists and user has access
     */
    async verifySessionAccess(sessionId: string, userId: string): Promise<boolean> {
        try {
            const { result: sessions, duration } = await measureTime(async () => {
                return await this.db
                    .select({ id: session.id })
                    .from(session)
                    .where(and(
                        eq(session.id, sessionId),
                        eq(session.userId, userId)
                    ))
                    .limit(1);
            });

            logDatabaseOperation('SELECT', 'session', duration, {
                sessionId,
                userId,
                hasAccess: sessions.length > 0,
            });

            return sessions.length > 0;
        } catch (error) {
            throw convertDatabaseError(error as Error, 'SELECT', 'session');
        }
    }

    async hasMessageBySession(sessionId: string): Promise<boolean> {
        try {
            const { result: messages, duration } = await measureTime(async () => {
                return await this.db
                    .select({ id: message.id })
                    .from(message)
                    .where(eq(message.sessionId, sessionId))
                    .limit(1);
            });

            logDatabaseOperation('SELECT', 'message', duration, {
                sessionId,
                hasMessage: messages.length > 0,
            });

            return messages.length > 0;
        } catch (error) {
            throw convertDatabaseError(error as Error, 'SELECT', 'message');
        }
    }

    async update(data: UpdateMessageEntity): Promise<MessageEntity | null> {
        try {
            const { result: updatedMessages, duration } = await measureTime(async () => {
                return await this.db.update(message).set(data).where(eq(message.dbId, data.dbId!)).returning();
            });

            logDatabaseOperation('UPDATE', 'message', duration, {
                messageDbId: data.dbId,
                updated: updatedMessages.length > 0,
            });
            return updatedMessages[0] ?? null;
        } catch (error) {
            throw convertDatabaseError(error as Error, 'UPDATE', 'message');
        }
    }

    /**
     * Update message with access control (优化版本)
     * 使用条件更新一次性完成权限验证和更新
     */
    async updateWithAccess(data: UpdateMessageEntity, userId: string): Promise<MessageEntity | null> {
        try {
            const { result: updatedMessages, duration } = await measureTime(async () => {
                return await this.db
                    .update(message)
                    .set(data)
                    .where(and(
                        eq(message.dbId, data.dbId!),
                        eq(message.userId, userId)
                    ))
                    .returning();
            });

            logDatabaseOperation('UPDATE', 'message', duration, {
                messageDbId: data.dbId,
                userId,
                updated: updatedMessages.length > 0,
            });

            return updatedMessages[0] ?? null;
        } catch (error) {
            throw convertDatabaseError(error as Error, 'UPDATE', 'message');
        }
    }

    /**
     * Find all messages by session without user filtering (for CSV export)
     */
    async findAllBySession(sessionId: string): Promise<MessageEntity[]> {
        try {
            const { result: messages, duration } = await measureTime(async () => {
                return await this.db
                    .select()
                    .from(message)
                    .where(eq(message.sessionId, sessionId))
                    .orderBy(desc(message.updatedAt));
            });

            logDatabaseOperation('SELECT', 'message', duration, {
                sessionId,
                operation: 'findAllBySession',
                count: messages.length,
            });

            return messages;
        } catch (error) {
            throw convertDatabaseError(error as Error, 'SELECT', 'message');
        }
    }
}