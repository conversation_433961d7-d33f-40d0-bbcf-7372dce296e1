/**
 * Message Data Transfer Objects
 * 
 * DTOs for message-related API operations with proper validation and typing.
 */

import { z } from 'zod/v4';

// Message content types (based on actual schema)
export const MessageContentTypeSchema = z.enum([
    'text',
    'file',
    'data'
]);

export type MessageContentType = z.infer<typeof MessageContentTypeSchema>;

// Message roles (based on actual schema)
export const MessageRoleSchema = z.enum(['user', 'assistant']);
export type MessageRole = z.infer<typeof MessageRoleSchema>;

// Base Message DTO
export const MessageDtoSchema = z.object({
    dbId: z.uuid(),
    id: z.string(),
    sessionId: z.uuid(),
    role: MessageRoleSchema,
    content: z.array(z.object({
        type: MessageContentTypeSchema,
        text: z.string().optional(),
        file: z.object({
            bytes: z.string().optional(),
            uri: z.string().optional(),
        }).optional(),
        data: z.union([z.record(z.string(), z.unknown()), z.string()]).optional(),
        extendedData: z.record(z.string(), z.unknown()).optional(),
    })).min(1),
    sender: z.object({
        id: z.string(),
        name: z.string().optional(),
        avatar: z.string().optional(),
        type: z.string().optional(),
    }).nullable(),
    extendedData: z.record(z.string(), z.unknown()).nullable(),
    createdAt: z.date(),
    updatedAt: z.date(),
    userId: z.string().nullable(),
});

export type MessageDto = z.infer<typeof MessageDtoSchema>;

// Create Message Request DTO
export const CreateMessageRequestDtoSchema = z.object({
    id: z.string(),
    sessionId: z.uuid(),
    role: MessageRoleSchema,
    content: z.array(z.object({
        type: MessageContentTypeSchema,
        text: z.string().optional(),
        file: z.object({
            bytes: z.string().optional(),
            uri: z.string().optional(),
        }).optional(),
        data: z.union([z.record(z.string(), z.unknown()), z.string()]).optional(),
        extendedData: z.record(z.string(), z.unknown()).optional(),
    })).min(1),
    sender: z.object({
        id: z.string(),
        name: z.string().optional(),
        avatar: z.string().optional(),
        type: z.string().optional(),
    }).nullable(),
    extendedData: z.record(z.string(), z.unknown()).optional(),
    tags: z.array(z.string()).optional(),
});

export type CreateMessageRequestDto = z.infer<typeof CreateMessageRequestDtoSchema>;

export const UpdateMessageRequestDtoSchema = z.object({
    sessionId: z.uuid().optional(),
    role: MessageRoleSchema.optional(),
    content: z.array(z.object({
        type: MessageContentTypeSchema,
        text: z.string().optional(),
        file: z.object({
            bytes: z.string().optional(),
            uri: z.string().optional(),
        }).optional(),
        data: z.union([z.record(z.string(), z.unknown()), z.string()]).optional(),
        extendedData: z.record(z.string(), z.unknown()).optional(),
    })).min(1),
    sender: z.object({
        id: z.string(),
        name: z.string().optional(),
        avatar: z.string().optional(),
        type: z.string().optional(),
    }).nullable().optional(),
    extendedData: z.record(z.string(), z.unknown()).optional(),
    tags: z.array(z.string()).optional(),
    updatedAt: z.date().optional(),
});

export type UpdateMessageRequestDto = z.infer<typeof UpdateMessageRequestDtoSchema>;

// 支持两种时间格式：YYYY-MM-DD HH:mm:ss 和时间戳字符串
const dateTimeFormat = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/;
const timestampFormat = /^\d+$/;

const stringToDate = z.preprocess((val) => {
    // 如果是 undefined、null 或空字符串，返回 undefined（可选）
    if (val === undefined || val === null || val === '') {
        return undefined;
    }
    return val;
}, z.string()
    .refine((val) => {
        return dateTimeFormat.test(val) || timestampFormat.test(val);
    }, {
        message: 'Invalid date-time format. Expected YYYY-MM-DD HH:mm:ss or timestamp string'
    })
    .transform((val) => {
        let date: Date;

        // 如果是纯数字字符串，当作时间戳处理
        if (timestampFormat.test(val)) {
            const timestamp = parseInt(val, 10);
            // 判断是秒级还是毫秒级时间戳
            date = timestamp < 10000000000 ? new Date(timestamp * 1000) : new Date(timestamp);
        } else {
            // 否则当作日期时间字符串处理
            date = new Date(val);
        }

        return date;
    })
    .refine((date) => {
        // 验证转换后的Date对象是否有效
        return date instanceof Date && !isNaN(date.getTime());
    }, {
        message: 'Invalid date value. Please provide a valid date or timestamp'
    }));

// Message List Query DTO (cursor-based pagination)
export const MessageListQueryDtoSchema = z.object({
    limit: z.coerce.number().min(1).max(5000).default(20),
    cursor: z.preprocess((val) => {
        // 如果是 undefined、null 或空字符串，返回 undefined（可选）
        if (val === undefined || val === null || val === '') {
            return undefined;
        }
        return val;
    }, z.uuid().optional()), // dbId for cursor-based pagination
    tags: z.preprocess((val) => {
        if (Array.isArray(val)) {
            return val.filter(item => typeof item === 'string');
        }
        if (typeof val === 'string') {
            // Handle both comma-separated strings and single strings
            return val.includes(',') ? val.split(',').map((s) => s.trim()) : [val];
        }
        return val;
    }, z.array(z.string()).optional()),
    startTime: stringToDate.optional(),
    endTime: stringToDate.optional(),
})
.refine(
    (data) => !data.endTime || !!data.startTime,
    {
        message: 'startTime is required when endTime is provided',
        path: ['startTime'],
    }
)
.refine(
    (data) => {
        if (!data.startTime || !data.endTime) {
            return true;
        }
        // 确保两个都是有效的Date对象，并且endTime >= startTime
        return data.endTime instanceof Date &&
               data.startTime instanceof Date &&
               data.endTime.getTime() >= data.startTime.getTime();
    },
    {
        message: 'endTime must be after or equal to startTime',
        path: ['endTime'],
    }
);



export type MessageListQueryDto = z.infer<typeof MessageListQueryDtoSchema>;

// Message Response DTO (for API responses)
export const MessageResponseDtoSchema = z.object({
    dbId: z.uuid(),
    id: z.string(),
    sessionId: z.string(),
    role: MessageRoleSchema,
    content: z.array(z.object({
        type: MessageContentTypeSchema,
        text: z.string().optional(),
        file: z.object({
            bytes: z.string().optional(),
            uri: z.string().optional(),
        }).optional(),
        data: z.union([z.record(z.string(), z.unknown()), z.string()]).optional(),
        extendedData: z.record(z.string(), z.unknown()).optional(),
    })),
    sender: z.object({
        id: z.string(),
        name: z.string().optional(),
        avatar: z.string().optional(),
        type: z.string().optional(),
    }).nullable(),
    extendedData: z.record(z.string(), z.unknown()).nullable(),
    createdAt: z.string(),
    updatedAt: z.string(),
    userId: z.string().nullable(),
});

export type MessageResponseDto = z.infer<typeof MessageResponseDtoSchema>;

// Message List Response DTO (cursor-based pagination)
export const MessageListResponseDtoSchema = z.object({
    messages: z.array(MessageResponseDtoSchema),
    hasNextPage: z.boolean(),
    nextCursor: z.uuid().optional(),
    limit: z.number().optional(),
});

export type MessageListResponseDto = z.infer<typeof MessageListResponseDtoSchema>;