import { injectable } from 'tsyringe';
import {
    Agent,
    AgentResponseDto,
    getAgentStatusDescription,
    getAgentTargetDescription,
    getAgentTypeDescription,
    getAgentGroupDescription,
    getAgentSessionModeDescription,
} from '../dto/agent-dto';
import { getDateTime } from '@/shared/utils';

@injectable()
export class AgentMapper {
    public toDto(agent: Agent): AgentResponseDto {
        return {
            ...agent,
            typeDescription: getAgentTypeDescription(agent.type),
            groupDescription: getAgentGroupDescription(agent.group),
            targetDescription: getAgentTargetDescription(agent.target),
            statusDescription: getAgentStatusDescription(agent.status),
            sessionModeDescription: getAgentSessionModeDescription(agent.isSingleSession),
            createdAt: getDateTime(agent.createdAt),
            updatedAt: getDateTime(agent.updatedAt),
            umdUrl: agent.umdUrl,
        };
    }

    public toDtoList(agents: Agent[]): AgentResponseDto[] {
        return agents.map(agent => {
            const dto = this.toDto(agent);
            return dto;
        });
    }
} 