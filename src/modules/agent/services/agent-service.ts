/**
 * Agent Service
 * 
 * Business logic layer for agent operations.
 * Handles agent creation, retrieval, update, and deletion with proper validation.
 */

import { injectable, inject } from 'tsyringe';
import { SmartErrorLogger } from '@/infrastructure/logger/smart-error-logger';
import { AgentRepository } from '../repositories/agent-repository';
import { AgentMapper } from './agent-mapper';
import { AuthorizationError, ValidationError, ResourceNotFoundError } from '@/shared/utils/errors';
import { TYPES } from '@/shared/constants';
import { SessionRepository } from '@/modules/session/repositories';
import {
    AgentStatus,
    CreateAgentRequestDto,
    UpdateAgentRequestDto,
    AgentListQueryDto,
    AgentResponseDto,
    type AgentListResponseDto,
    type AgentInsert,
    type AgentUpdate,
} from '../dto/agent-dto';

@injectable()
export class AgentService {
    constructor(
        @inject(TYPES.AgentRepository) private readonly agentRepository: AgentRepository,
        @inject(TYPES.AgentMapper) private readonly agentMapper: AgentMapper,
        @inject(TYPES.SessionRepository) private readonly sessionRepository: SessionRepository
    ) {}

    /**
     * Get all agents with pagination and filtering
     * 优化版本：减少数据库查询次数
     */
    async getAgents(query: AgentListQueryDto, userId: string): Promise<AgentListResponseDto> {
        const { agents, total } = await this.agentRepository.list(query, userId);
        const agentDtos = this.agentMapper.toDtoList(agents);

        // 性能优化：只对单会话模式的agent进行批量会话状态查询
        const singleSessionAgentIds = agentDtos
            .filter(agent => agent.isSingleSession)
            .map(agent => agent.id);

        let sessionStatusMap: Map<string, boolean> = new Map();

        if (singleSessionAgentIds.length > 0) {
            try {
                // 批量查询所有单会话agent的会话状态
                sessionStatusMap = await this.batchCheckSessionStatus(singleSessionAgentIds, userId);
            } catch (error) {
                // 如果批量查询失败，记录错误但不影响agent列表返回
                const logContext = {
                    userId,
                    agentIds: singleSessionAgentIds,
                    method: 'getAgents-batchCheckSession',
                    url: '/agent'
                };
                SmartErrorLogger.logError(error as Error, logContext, 'service');
            }
        }

        // 为agent添加会话状态
        const agentDtosWithSessionStatus = agentDtos.map(agentDto => {
            if (agentDto.isSingleSession) {
                return {
                    ...agentDto,
                    hasCreatedSession: sessionStatusMap.get(agentDto.id) ?? false
                };
            }
            return agentDto;
        });

        const totalPages = Math.ceil(total / query.limit);
        const pagination = {
            page: query.page,
            limit: query.limit,
            total,
            totalPages,
            hasNext: query.page < totalPages,
            hasPrev: query.page > 1,
        };

        return { agents: agentDtosWithSessionStatus, pagination };
    }

    /**
     * 批量检查多个agent的会话创建状态
     * 性能优化：使用单次IN查询替代多次查询
     */
    private async batchCheckSessionStatus(agentIds: string[], userId: string): Promise<Map<string, boolean>> {
        if (agentIds.length === 0) {
            return new Map();
        }

        try {
            // 使用SessionRepository的高效批量查询方法
            return await this.sessionRepository.batchCheckAgentsHaveSessions(agentIds, userId);
        } catch (error) {
            // 如果批量查询失败，记录错误并返回默认值
            const logContext = {
                userId,
                agentIds: agentIds.length,
                method: 'batchCheckSessionStatus',
                url: '/agent'
            };
            SmartErrorLogger.logError(error as Error, logContext, 'service');

            // 返回空Map（所有agent默认为false）
            const statusMap = new Map<string, boolean>();
            agentIds.forEach(agentId => statusMap.set(agentId, false));
            return statusMap;
        }
    }

    /**
     * Get agent by ID with access control
     */
    async getAgentById(agentId: string, userId: string): Promise<AgentResponseDto> {
        if (!agentId) {
            throw new ValidationError('Agent ID are required');
        }
        
        const agent = await this.agentRepository.findById(agentId);

        if (!agent) {
            // 使用SmartErrorLogger记录业务异常，避免重复日志
            const logContext = { requestId: 'unknown', userId, method: 'getAgentById', url: '/agent' };
            SmartErrorLogger.logError(
                new ResourceNotFoundError('Agent', 'Agent not found'),
                logContext,
                'service'
            );
            throw new ResourceNotFoundError('Agent', 'Agent not found');
        }

        return this.agentMapper.toDto(agent);
    }

    /**
     * Create new agent
     */
    async createAgent(data: CreateAgentRequestDto, userId: string): Promise<AgentResponseDto> {
        const agentToCreate: AgentInsert = {
            ...data,
            description: data.description ?? null,
            userId,
            status: AgentStatus.ACTIVE,
            group: data.group,
        };
        
        const agent = await this.agentRepository.create(agentToCreate, userId);
        
        return this.agentMapper.toDto(agent);
    }

    /**
     * Update agent with access control
     */
    async updateAgent(agentId: string, data: UpdateAgentRequestDto, userId: string): Promise<AgentResponseDto> {
        const existingAgent = await this.agentRepository.findById(agentId);
        if (!existingAgent) {
            throw new ResourceNotFoundError('Agent not found');
        }

        const agentToUpdate: AgentUpdate = { ...data };
        const updatedAgent = await this.agentRepository.update(agentId, agentToUpdate, userId);
        
        if (!updatedAgent) {
            throw new ResourceNotFoundError('Agent not found after update');
        }
        
        return this.agentMapper.toDto(updatedAgent);
    }

    /**
     * Delete agent with access control
     */
    async deleteAgent(agentId: string, userId: string): Promise<{ id: string }> {        
        const existingAgent = await this.agentRepository.findById(agentId);
        if (!existingAgent) {
            throw new ResourceNotFoundError('Agent not found');
        }

        if (existingAgent.userId !== userId) {
            throw new AuthorizationError('User is not authorized to delete this agent');
        }

        const deleted = await this.agentRepository.delete(agentId, userId);
        
        if (!deleted) {
            throw new ResourceNotFoundError('Agent not found');
        }

        return { id: agentId };
    }
} 