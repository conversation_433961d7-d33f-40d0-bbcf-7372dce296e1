/**
 * Agent Repository
 * 
 * Handles database operations for agents with proper error handling and logging.
 */

import { injectable } from 'tsyringe';
import { eq, and, desc, asc, inArray, sql } from 'drizzle-orm';
import { databaseService } from '@/infrastructure/database';
import { agent } from '@/infrastructure/database/schema/agent';
import { logDatabaseOperation } from '@/infrastructure/logger';
import { measureTime } from '@/shared/utils/time';
import type {
    Agent,
    AgentInsert,
    AgentUpdate,
    AgentListQueryDto,
} from '../dto/agent-dto';
import type { PostgresJsDatabase } from 'drizzle-orm/postgres-js';
import { convertDatabaseError } from '@/shared/utils/errors';

@injectable()
export class AgentRepository {
    constructor() { }

    private get db(): PostgresJsDatabase<Record<string, unknown>> {
        return databaseService.getDb();
    }

    getDb(): PostgresJsDatabase<Record<string, unknown>> {
        return this.db;
    }

    /**
     * Create a new agent
     */
    async create(data: AgentInsert, userId: string): Promise<Agent> {
        const agentData: AgentInsert = {
            ...data,
            userId,
        };

        try {
            const { result: createdAgents, duration } = await measureTime(async () => {
                return await this.db
                    .insert(agent)
                    .values(agentData)
                    .returning();
            });

            const newAgent = createdAgents[0];
            if (!newAgent) {
                throw new Error('Failed to create agent');
            }

            logDatabaseOperation('INSERT', 'agent', duration, {
                agentId: newAgent.id,
                userId,
            });

            return newAgent;
        } catch (error) {
            throw convertDatabaseError(error as Error, 'INSERT', 'agent');
        }
    }

    /**
     * Get agent by ID with user access control
     */
    async findById(id: string): Promise<Agent | null> {
        const conditions = [eq(agent.id, id)];
        try {
            const { result: agents, duration } = await measureTime(async () => {
                return await this.db
                    .select()
                    .from(agent)
                    .where(and(...conditions))
                    .limit(1);
            });

            logDatabaseOperation('SELECT', 'agent', duration, {
                agentId: id,
                found: agents.length > 0,
            });

            return agents[0] ?? null;
        } catch (error) {
            throw convertDatabaseError(error as Error, 'SELECT', 'agent');
        }
    }

    /**
     * Get agent by ID with user access control
     */
    async findByIds(ids: string[]): Promise<Agent[]> {
        try {
            const { result: agents, duration } = await measureTime(async () => {
                return await this.db
                    .select()
                    .from(agent)
                    .where(inArray(agent.id, ids));
            });

            logDatabaseOperation('SELECT', 'agent', duration, {
                agentIds: ids,
                found: agents.length > 0,
            });
            return agents;
        } catch (error) {
            throw convertDatabaseError(error as Error, 'SELECT', 'agent');
        }
    }

    /**
     * Update agent with user access control
     */
    async update(id: string, data: Partial<AgentUpdate>, userId: string): Promise<Agent | null> {
        try {
            const { result: updatedAgents, duration } = await measureTime(async () => {
                return await this.db
                    .update(agent)
                    .set({
                        ...data,
                        updatedAt: new Date(), // Explicitly set updatedAt to current timestamp
                    })
                    .where(and(
                        eq(agent.id, id),
                    ))
                    .returning();
            });

            logDatabaseOperation('UPDATE', 'agent', duration, {
                agentId: id,
                userId,
                updated: updatedAgents.length > 0,
            });

            return updatedAgents[0] ?? null;
        } catch (error) {
            throw convertDatabaseError(error as Error, 'UPDATE', 'agent');
        }
    }

    /**
     * Soft delete agent (set status to 0)
     */
    async delete(id: string, userId: string): Promise<boolean> {
        try {
            const { result: deletedAgents, duration } = await measureTime(async () => {
                return await this.db
                    .update(agent)
                    .set({
                        status: 0, // Soft delete
                        updatedAt: new Date(), // Update timestamp
                    })
                    .where(and(
                        eq(agent.id, id),
                        eq(agent.userId, userId)
                    ))
                    .returning();
            });

            logDatabaseOperation('UPDATE', 'agent', duration, {
                agentId: id,
                userId,
                deleted: deletedAgents.length > 0,
                operation: 'soft_delete',
            });

            return deletedAgents.length > 0;
        } catch (error) {
            throw convertDatabaseError(error as Error, 'UPDATE', 'agent');
        }
    }

    /**
     * List agents with filtering and pagination
     * If userId is provided, includes latest 2 sessions for each agent
     */
    async list(query: AgentListQueryDto, userId?: string): Promise<{ agents: Agent[]; total: number }> {
        const {
            page = 1,
            limit = 10,
            type,
            group,
            target,
            status,
            sortBy = 'createdAt',
            sortOrder = 'desc',
            includeDeleted = false,
            isSingleSession
        } = query;

        const offset = (page - 1) * limit;
        const conditions = [];

        // Filter by status
        if (status !== undefined) {
            conditions.push(eq(agent.status, status));
        } else if (!includeDeleted) {
            conditions.push(eq(agent.status, 1)); // Only active
        }

        // Filter by session mode
        if (isSingleSession !== undefined) {
            conditions.push(eq(agent.isSingleSession, isSingleSession));
        }

        // Filter by type
        if (type !== undefined) {
            conditions.push(eq(agent.type, type));
        }

        // Filter by group
        if (group !== undefined) {
            conditions.push(eq(agent.group, group));
        }

        // Filter by target
        if (target !== undefined) {
            conditions.push(eq(agent.target, target));
        }

        const whereCondition = conditions.length > 0 ? and(...conditions) : undefined;

        try {
            // Get agents with sorting
            const sortColumn = sortBy === 'name' ? agent.name :
                sortBy === 'type' ? agent.type :
                    sortBy === 'updatedAt' ? agent.updatedAt :
                        agent.createdAt;

            const sortDirection = sortOrder === 'asc' ? asc(sortColumn) : desc(sortColumn);

            // 使用窗口函数一次性获取数据和总数，避免两次查询
            const { result: results, duration } = await measureTime(async () => {
                return await this.db
                    .select({
                        agent: agent,
                        totalCount: sql<number>`count(*) over()`.as('total_count')
                    })
                    .from(agent)
                    .where(whereCondition)
                    .orderBy(sortDirection)
                    .limit(limit)
                    .offset(offset);
            });

            const agents = results.map(r => r.agent);
            const totalCount = results.length > 0 ? Number(results[0]!.totalCount) : 0;

            logDatabaseOperation('SELECT', 'agent', duration, {
                userId: userId ?? 'anonymous',
                total: totalCount,
                page,
                limit,
                count: agents.length,
                operation: 'list_with_count_optimized', // 标记为优化版本
            });

            return {
                agents: agents,
                total: totalCount,
            };
        } catch (error) {
            throw convertDatabaseError(error as Error, 'SELECT', 'agent');
        }
    }
} 