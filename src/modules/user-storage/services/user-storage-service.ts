/**
 * User Storage Service
 * 
 * Business logic layer for user storage operations.
 * Handles user storage CRUD operations based on key.
 */

import { injectable, inject } from 'tsyringe';
import { CreateUserStorageEntity, UserStorageRepository } from '../repositories/user-storage-repository';
import { UserStorageMapper } from './user-storage-mapper';
import { TYPES } from '@/shared/constants';
import type { 
    CreateOrUpdateUserStorageRequestDto,
    UserStorageResponseDto,
} from '../dto';
import { ResourceNotFoundError } from '@/shared/utils/errors';

@injectable()
export class UserStorageService {
    constructor(
        @inject(TYPES.UserStorageRepository) private readonly userStorageRepository: UserStorageRepository,
        @inject(TYPES.UserStorageMapper) private readonly userStorageMapper: UserStorageMapper
    ) {}

    /**
     * Get user storage by key
     */
    async getByKey(key: string, userId: string): Promise<UserStorageResponseDto> {
        const storage = await this.userStorageRepository.findByKey(key, userId);
        
        if (!storage) {
            // 使用SmartErrorLogger记录业务异常，避免重复日志
            throw new ResourceNotFoundError('User storage', 'User storage not found');
        }

        return this.userStorageMapper.toDto(storage);
    }

    /**
     * Create user storage by key
     */
    async createByKey(
        key: string, 
        userId: string, 
        data: CreateOrUpdateUserStorageRequestDto
    ): Promise<UserStorageResponseDto | null> {
        const storageData: CreateUserStorageEntity = {
            userId,
            key,
            value: data.value,
            description: data.description,
            version: data.version ?? '1.0',
            expiresAt: data.expiresAt ?? null,
        };

        const existing = await this.userStorageRepository.findByKey(key, userId);
        if (existing) {
            const updated = await this.userStorageRepository.update(key, {
                value: data.value,
                description: data.description,
                version: data.version ?? '1.0',
                expiresAt: data.expiresAt ?? null,
            });
            if (!updated) {
                throw new ResourceNotFoundError('User storage not found after update');
            }
            return this.userStorageMapper.toDto(updated);
        }

        const storage = await this.userStorageRepository.create(storageData);

        return this.userStorageMapper.toDto(storage);
    }

    /**
     * Update user storage by key
     */
    async updateByKey(
        key: string, 
        userId: string,
        data: CreateOrUpdateUserStorageRequestDto
    ): Promise<UserStorageResponseDto | null> {
        // First check if the storage exists
        const existing = await this.userStorageRepository.findByKey(key, userId);
        if (!existing) {
            throw new ResourceNotFoundError('User storage not found');
        }

        const storage = await this.userStorageRepository.update(key, data);

        if(!storage){
            throw new ResourceNotFoundError('User storage not found after update');
        }

        return this.userStorageMapper.toDto(storage);
    }

    /**
     * Delete user storage by key
     */
    async deleteByKey(key: string, userId: string): Promise<{key:string}> {
        const existing = await this.userStorageRepository.findByKey(key, userId);
        if (!existing) {
            throw new ResourceNotFoundError('User storage not found');
        }

        const deleted = await this.userStorageRepository.deleteByKey(key, userId);

        if (!deleted) {
            throw new ResourceNotFoundError('User storage not found for deletion');
        }

        return {
            key
        };
    }
} 