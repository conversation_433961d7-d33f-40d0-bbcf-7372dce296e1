/**
 * User Storage Controller
 * 
 * Handles HTTP requests for user storage operations.
 */

import { injectable, inject } from 'tsyringe';
import { BaseController } from '@/shared/utils/base-controller';
import { UserStorageService } from '../services/user-storage-service';
import { TYPES } from '@/shared/constants';
import type {
    CreateOrUpdateUserStorageRequestDto,
} from '../dto';
import type { AuthenticatedRequest } from '@/shared/types';
import type { Response } from 'express';

@injectable()
export class UserStorageController extends BaseController {
    constructor(@inject(TYPES.UserStorageService) private readonly userStorageService: UserStorageService) {
        super();
    }

    /**
     * Get user storage by key
     * GET /user-storage/:key
     */
    public getByKey = this.asyncHandler(
        async (req: AuthenticatedRequest, res: Response): Promise<void> => {
            const { key } = req.validatedParams as { key: string };
            const userId = this.getUserId(req);
            
            const storage = await this.userStorageService.getByKey(key, userId);
            this.sendSuccess(res, storage, 'User storage retrieved successfully');
        }
    );

    /**
     * Create user storage by key
     * PUT /user-storage/:key
     */
    public createByKey = this.asyncHandler(
        async (req: AuthenticatedRequest, res: Response): Promise<void> => {
            const { key } = req.validatedParams as { key: string };
            const createDto = req.validatedBody as unknown as CreateOrUpdateUserStorageRequestDto;
            const userId = this.getUserId(req);
            
            const storage = await this.userStorageService.createByKey(key, userId, createDto);
            
            this.sendSuccess(res, storage, 'User storage created successfully');
        }
    );

    /**
     * Update user storage by key
     * PATCH /user-storage/:key
     */
    public updateByKey = this.asyncHandler(
        async (req: AuthenticatedRequest, res: Response): Promise<void> => {
            const { key } = req.validatedParams as { key: string };
            const updateDto = req.validatedBody as CreateOrUpdateUserStorageRequestDto;
            const userId = this.getUserId(req);
            
            const storage = await this.userStorageService.updateByKey(key, userId, updateDto);
            
            this.sendSuccess(res, storage, 'User storage updated successfully');
        }
    );

    /**
     * Delete user storage by key
     * DELETE /user-storage/:key
     */
    public deleteByKey = this.asyncHandler(
        async (req: AuthenticatedRequest, res: Response): Promise<void> => {
            const { key } = req.validatedParams as { key: string };
            const userId = this.getUserId(req);
            
            const deleted = await this.userStorageService.deleteByKey(key, userId);
            
            this.sendSuccess(res, deleted, 'User storage deleted successfully');
        }
    );
} 