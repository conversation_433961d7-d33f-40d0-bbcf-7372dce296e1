/* eslint-disable no-console */
/**
 * 应用程序入口点
 *
 * 此文件是应用程序的主要入口点。它使用新的启动器来确保
 * 组件按正确顺序初始化，避免循环依赖问题。
 *
 * 启动顺序：
 * 1. 加载基础环境变量
 * 2. 根据配置决定是否加载 Nacos 配置
 * 3. 初始化日志系统
 * 4. 初始化数据库连接
 * 5. 初始化依赖注入容器
 * 6. 创建和配置 Express 应用
 * 7. 启动 HTTP 服务器
 */

import 'reflect-metadata';
import { applicationBootstrap } from '@/infrastructure/bootstrap/application-bootstrap';

/**
 * 主函数 - 应用程序入口点
 */
const main = async (): Promise<void> => {
    try {
        // 使用新的启动器启动应用
        await applicationBootstrap.start();
    } catch (error) {
        console.error('❌ Failed to start application:', error);
        process.exit(1);
    }
};

// 启动应用程序
main().catch((error) => {
    console.error('❌ Unhandled error in main:', error);
    process.exit(1);
});
