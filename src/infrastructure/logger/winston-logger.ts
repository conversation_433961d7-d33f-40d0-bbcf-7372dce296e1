/**
 * <PERSON>日志配置
 * 
 * 使用Winston的集中式日志服务，支持结构化日志。
 */

import winston from 'winston';
import { getLoggingConfig } from '@/infrastructure/config';
import type { LogContext } from '@/shared/types';

/**
 * 日志级别配置
 */
const LOG_LEVELS = {
    error: 0,
    warn: 1,
    info: 2,
    http: 3,
    debug: 4,
} as const;

/**
 * 控制台格式（开发环境）
 */
const consoleFormat = winston.format.combine(
    winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss.SSS' }),
    winston.format.errors({ stack: true }),
    winston.format.colorize(),
    winston.format.printf(({ level, message, timestamp, service, ...meta }) => {
        const metaStr = Object.keys(meta).length ? ` ${JSON.stringify(meta)}` : '';
        return `[${timestamp}] [${service ?? 'xui-app-server'}] ${level.toUpperCase()} ${message}${metaStr}`;
    })
);

/**
 * 文件格式（生产环境，符合filebeat和logstash要求）
 */
const fileFormat = winston.format.combine(
    winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss.SSS' }),
    winston.format.errors({ stack: true }),
    winston.format.printf(({ level, message, timestamp, service, ...meta }) => {
        const serviceName = service ?? 'xui-app-server';
        const metaStr = Object.keys(meta).length > 0 ? ` ${JSON.stringify(meta)}` : '';
        // 格式: [时间] [服务名] 日志级别 消息 元数据
        return `[${timestamp}] [${serviceName}] ${level.toUpperCase()} ${message}${metaStr}`;
    })
);

/**
 * 创建传输器
 */
const createTransports = (): winston.transport[] => {
    const loggingConfig = getLoggingConfig();
    const transports: winston.transport[] = [];

    // 控制台传输器（开发环境使用）
    if (loggingConfig['format'] === 'console') {
        transports.push(
            new winston.transports.Console({
                format: consoleFormat,
            })
        );
    } else {
        // 统一文件传输器（所有日志输出到同一个文件）
        transports.push(
            new winston.transports.File({
                filename: loggingConfig['combinedLogFile'] as string,
                format: fileFormat,
                maxsize: 10485760, // 10MB
                maxFiles: 10,
                // 关键优化：启用异步写入
                options: { flags: 'a', highWaterMark: 16 * 1024 }
            })
        );
    }

    return transports;
};

/**
 * 创建Winston日志实例
 */
const createLogger = (): winston.Logger => {
    const loggingConfig = getLoggingConfig();
    return winston.createLogger({
        level: loggingConfig['level'] as string,
        levels: LOG_LEVELS,
        defaultMeta: {
            service: 'xui-app-server',
        },
        transports: createTransports(),
        exitOnError: false,
    });
};

// 延迟初始化的日志实例
let winstonLogger: winston.Logger | null = null;

/**
 * 获取日志实例（延迟初始化）
 */
const getLoggerInstance = (): winston.Logger => {
    winstonLogger ??= createLogger();
    return winstonLogger;
};

/**
 * 增强的日志功能，支持上下文
 */
export class Logger {
    private static get instance(): winston.Logger {
        return getLoggerInstance();
    }

    /**
     * 记录错误消息
     */
    public static error(message: string, context?: LogContext, error?: Error): void {
        Logger.instance.error(message, {
            ...context,
            ...(error && {
                error: {
                    name: error.name,
                    message: error.message,
                    stack: error.stack,
                },
            }),
        });
    }

    /**
     * 记录警告消息
     */
    public static warn(message: string, context?: LogContext): void {
        Logger.instance.warn(message, context);
    }

    /**
     * 记录信息消息
     */
    public static info(message: string, context?: LogContext): void {
        Logger.instance.info(message, context);
    }

    /**
     * 记录HTTP请求
     */
    public static http(message: string, context?: LogContext): void {
        Logger.instance.http(message, context);
    }

    /**
     * 记录调试消息
     */
    public static debug(message: string, context?: LogContext): void {
        Logger.instance.debug(message, context);
    }

    /**
     * 获取日志实例
     */
    public static getInstance(): winston.Logger {
        return Logger.instance;
    }

    /**
     * 创建带默认上下文的子日志器
     */
    public static child(defaultContext: LogContext): winston.Logger {
        return Logger.instance.child(defaultContext);
    }
}

// 导出便利函数
export const logError = Logger.error.bind(Logger);
export const logWarn = Logger.warn.bind(Logger);
export const logInfo = Logger.info.bind(Logger);
export const logHttp = Logger.http.bind(Logger);
export const logDebug = Logger.debug.bind(Logger);

/**
 * 数据库操作日志记录器
 */
export const logDatabaseOperation = (
    operation: string,
    table: string,
    duration?: number,
    context?: LogContext
): void => {
    const durationText = (duration !== undefined && duration > 0) ? ` (${duration}ms)` : '';
    Logger.info(`Database operation: ${operation} on ${table}${durationText}`, {
        ...context,
        operation,
        table,
        ...(duration !== undefined && duration > 0 && { duration }),
    });
};

/**
 * Morgan HTTP日志记录流
 */
export const logStream = {
    write: (message: string): void => {
        Logger.http(message.trim());
    },
};

// 注意：为避免循环依赖，建议直接使用 Logger 类的静态方法