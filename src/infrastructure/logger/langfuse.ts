/**
 * Langfuse集成服务
 * 
 * 提供Langfuse客户端初始化和配置。
 */

import { Langfuse } from 'langfuse';
import { Logger } from './winston-logger';
import type {
    LangfuseTrace,
} from '@/shared/types';
import { getLangfuseConfig, getRunEnvCongfig } from '../config';
import { LangfuseEventConfig } from '@/shared/types/langfuse-types';

/**
 * Langfuse服务类
 */
export class LangfuseService {
    private client: Langfuse | null = null;
    private readonly enabled: boolean;

    constructor() {
        // 延迟初始化配置检查
        const config = this.getLangfuseConfig();
        this.enabled = config['enabled'] as boolean;
        this.initialize();
    }

    /**
     * 获取Langfuse配置
     */
    private getLangfuseConfig(): Record<string, unknown> {
        const config = getLangfuseConfig();
        return config;
    }

    /**
     * 初始化Langfuse客户端
     */
    private initialize(): void {
        if (!this.enabled) {
            Logger.info('Langfuse client not configured');
            return;
        }

        try {
            const config = this.getLangfuseConfig();
            this.client = new Langfuse({
                publicKey: config['publicKey'] as string,
                secretKey: config['secretKey'] as string,
                baseUrl: config['baseUrl'] as string,
                requestTimeout: 10000, // 10秒超时
            });

            // 添加全局错误处理，捕获SDK内部错误
            this.setupErrorHandling();

            Logger.info(`Langfuse client baseUrl:${config['baseUrl']}`);
            Logger.info('Langfuse client initialized successfully');
        } catch (error) {
            Logger.warn('Failed to initialize Langfuse client', {   error: error as Error });
        }
    }

    /**
     * 设置错误处理，捕获SDK内部错误
     */
    private setupErrorHandling(): void {
        if (!this.client) {
            return;
        }

        // 捕获未处理的Promise拒绝（来自Langfuse SDK）
        // eslint-disable-next-line no-console
        const originalConsoleError = console.error;
        // eslint-disable-next-line no-console
        console.error = (...args: unknown[]): void => {
            const message = args.join(' ');

            // 过滤Langfuse SDK的JSON解析错误
            if (message.includes('[Langfuse SDK]') &&
                (message.includes('is not valid JSON') ||
                    message.includes('Unexpected token') ||
                    message.includes('SyntaxError'))) {

                Logger.warn('Langfuse server returned non-JSON response, likely a server error page', {
                    error: 'Server returned HTML instead of JSON'
                });
                return;
            }

            // 其他错误正常输出
            originalConsoleError.apply(console, args);
        };
    }

    /**
     * 检查Langfuse是否启用并配置
     */
    public isEnabled(): boolean {
        return this.enabled && this.client !== null;
    }

    /**
     * 创建新的追踪
     */
    public createTrace(options: Record<string, unknown>): LangfuseTrace | null {
        if (!this.isEnabled() || !this.client) {
            Logger.debug('Langfuse not enabled or client not available for trace creation');
            return null;
        }

        const name = options['name'];
        const input = options['input'];
        const metadata = options['metadata'];
        const runEnv = getRunEnvCongfig();
        const tags: string[] = [];
        if (runEnv !== undefined && runEnv.trim() !== '') {
            tags.push(runEnv);
        }

        try {
            const trace = this.client.trace({
                ...options,
                tags: tags
            }) as LangfuseTrace;

            Logger.debug('Langfuse trace created successfully', {
                name,
                traceId: trace.id,
                hasInput: input !== undefined,
                hasMetadata: metadata !== undefined
            });

            return trace;
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);

            Logger.warn(
                'Langfuse trace creation failed due to server response format issue',
                { name, errorMessage }
            );
            return null;
        }
    }

    public updateTrace(trace: LangfuseTrace, data: Record<string, unknown>): void {
        if (!this.isEnabled() || !this.client) {
            return;
        }

        try {
            trace.update(data);
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            Logger.warn('Failed to update Langfuse trace', { traceId: trace.id, errorMessage });
        }
    }

    public createEvent(trace: LangfuseTrace, data: Record<string, unknown>): void {
        if (!this.isEnabled() || !this.client) {
            return;
        }

        try {
            trace.event(data as unknown as LangfuseEventConfig);
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            Logger.warn('Failed to create Langfuse event', { traceId: trace.id, errorMessage });
        }
    }

    /**
     * 异步刷新待处理事件
     */
    public async flushAsync(): Promise<void> {
        if (!this.isEnabled() || !this.client) {
            return;
        }

        try {
            await this.client.flushAsync();
        } catch (error) {
            Logger.warn('Failed to flush Langfuse events', { error:error });
        }
    }

    /**
     * 刷新待处理事件（同步版本，调用异步方法）
     */
    public async flush(): Promise<void> {
        return this.flushAsync();
    }

    /**
     * 关闭Langfuse客户端
     */
    public async shutdown(): Promise<void> {

        if (!this.isEnabled() || !this.client) {
            Logger.debug('Langfuse client not enabled or already shutdown');
            return;
        }

        try {
            Logger.info('Shutting down Langfuse client...');
            // 最终刷新所有待处理事件
            await this.flushAsync();
            // 关闭客户端
            await this.client.shutdownAsync();
            Logger.info('Langfuse client shutdown successfully');
        } catch (error) {
            Logger.warn('Failed to shutdown Langfuse client', {error:error});
        } finally {
            this.client = null;
        }
    }

    /**
     * 获取客户端实例
     */
    public getClient(): Langfuse | null {
        return this.client;
    }
}

// 延迟初始化的单例实例
let langfuseInstance: LangfuseService | null = null;

/**
 * 获取 Langfuse 服务实例（延迟初始化）
 */
export function getLangfuseService(): LangfuseService {
    langfuseInstance ??= new LangfuseService();
    return langfuseInstance;
}

// 为避免循环依赖，请使用 getLangfuseService() 函数获取实例
export default getLangfuseService; 