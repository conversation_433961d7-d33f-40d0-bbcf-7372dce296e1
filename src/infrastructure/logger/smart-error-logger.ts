/**
 * 智能错误日志记录器
 * 
 * 解决过度错误日志记录问题：
 * 1. 避免重复日志记录
 * 2. 业务异常使用合适的日志级别
 * 3. 减少不必要的上下文信息
 */

import { Logger } from './winston-logger';
import { AppError } from '@/shared/utils/errors';
import type { LogContext } from '@/shared/types';

interface ErrorRecord {
    count: number;
    firstSeen: number;
    lastLogged: number;
}

export class SmartErrorLogger {
    private static readonly errorRecords = new Map<string, ErrorRecord>();
    private static readonly LOG_INTERVAL_MS = 30000; // 30秒内最多记录一次
    private static readonly CLEANUP_INTERVAL_MS = 180000; // 3分钟清理一次记录（优化：缩短清理间隔）
    private static cleanupTimer: NodeJS.Timeout | null = null;
    private static readonly MAX_RECORDS = 500; // 最大记录数限制（优化：降低最大记录数）

    /**
     * 智能错误日志记录
     */
    static logError(
        error: Error,
        context: LogContext,
        source: 'controller' | 'middleware' | 'service' = 'controller'
    ): void {
        // 检查是否应该记录
        if (!this.shouldLog(error, source)) {
            return;
        }

        // 生成错误指纹
        const errorFingerprint = this.generateErrorFingerprint(error, context);
        const now = Date.now();

        // 获取或创建错误记录
        let errorRecord = this.errorRecords.get(errorFingerprint);
        if (!errorRecord) {
            errorRecord = {
                count: 0,
                firstSeen: now,
                lastLogged: 0
            };
            this.errorRecords.set(errorFingerprint, errorRecord);
        }

        // 增加错误计数
        errorRecord.count++;

        // 检查是否应该记录日志（首次出现或距离上次记录超过间隔时间）
        const shouldLogNow = errorRecord.count === 1 ||
                           (now - errorRecord.lastLogged) >= this.LOG_INTERVAL_MS;

        if (shouldLogNow) {
            // 构建日志上下文，包含错误统计信息
            const logContext = this.buildLogContext(error, context, errorRecord);

            // 根据错误类型选择日志级别
            if (error instanceof AppError && error.isBusinessError) {
                Logger.info(error.message, logContext);
            } else {
                Logger.error(error.message, logContext, error);
            }

            errorRecord.lastLogged = now;
        }

        // 启动定期清理（如果尚未启动）
        this.ensureCleanupStarted();

        // 检查是否需要立即清理（记录数过多时）
        if (this.errorRecords.size > this.MAX_RECORDS) {
            this.cleanupOldRecords();
        }
    }

    /**
     * 生成错误指纹
     */
    private static generateErrorFingerprint(error: Error, context: LogContext): string {
        return `${error.name}:${error.message}:${context.userId}:${context.url}`;
    }

    /**
     * 判断是否应该记录日志
     */
    private static shouldLog(error: Error, source: string): boolean {
        // 业务异常只在服务层记录一次，避免重复
        if (error instanceof AppError && error.isBusinessError && source !== 'service') {
            return false;
        }
        return true;
    }

    /**
     * 构建精简的日志上下文，包含错误统计信息
     */
    private static buildLogContext(error: Error, context: LogContext, errorRecord: ErrorRecord): LogContext {
        const baseContext = {
            requestId: context.requestId ?? 'unknown',
            userId: context.userId ?? 'anonymous',
            errorCount: errorRecord.count,
            ...(errorRecord.count > 1 && {
                firstSeen: new Date(errorRecord.firstSeen).toISOString(),
                occurrencePattern: this.getOccurrencePattern(errorRecord)
            })
        };

        // 业务异常只记录基本信息
        if (error instanceof AppError && error.isBusinessError) {
            return baseContext;
        }

        // 系统错误记录更多技术信息，包括所有额外的上下文字段
        const systemErrorContext: LogContext = {
            ...baseContext,
            ...context,
            method: context.method ?? 'unknown',
            url: context.url ?? 'unknown',
        };
        return systemErrorContext;
    }

    /**
     * 获取错误发生模式描述
     */
    private static getOccurrencePattern(errorRecord: ErrorRecord): string {
        const duration = Date.now() - errorRecord.firstSeen;
        const minutes = Math.floor(duration / 60000);

        if (minutes === 0) {
            return `${errorRecord.count} times in less than 1 minute`;
        } else {
            const rate = Math.round(errorRecord.count / minutes);
            return `${errorRecord.count} times in ${minutes} minutes (${rate}/min)`;
        }
    }

    /**
     * 确保清理定时器已启动
     */
    private static ensureCleanupStarted(): void {
        this.cleanupTimer ??= setInterval(() => {
            this.cleanupOldRecords();
        }, 60000); // 每分钟清理一次
    }

    /**
     * 清理旧的错误记录
     */
    private static cleanupOldRecords(): void {
        const now = Date.now();
        let deletedCount = 0;

        for (const [fingerprint, record] of this.errorRecords.entries()) {
            // 清理5分钟前的记录
            if (now - record.firstSeen > this.CLEANUP_INTERVAL_MS) {
                this.errorRecords.delete(fingerprint);
                deletedCount++;
            }
        }

        // 如果记录数量仍然过多，删除最旧的记录
        if (this.errorRecords.size > this.MAX_RECORDS) {
            const sortedEntries = Array.from(this.errorRecords.entries())
                .sort(([, a], [, b]) => a.firstSeen - b.firstSeen);

            const toDelete = sortedEntries.slice(0, this.errorRecords.size - this.MAX_RECORDS);
            for (const [fingerprint] of toDelete) {
                this.errorRecords.delete(fingerprint);
                deletedCount++;
            }
        }

        if (deletedCount > 0) {
            Logger.info(`SmartErrorLogger: Cleaned up ${deletedCount} old error records`);
        }
    }

    /**
     * 启动定期清理
     */
    public static startPeriodicCleanup(): void {
        if (this.cleanupTimer) {
            return; // 已经启动
        }

        this.cleanupTimer = setInterval(() => {
            this.cleanupOldRecords();
        }, 60000); // 每分钟清理一次

        Logger.info('SmartErrorLogger: Started periodic cleanup');
    }

    /**
     * 停止定期清理
     */
    public static stopPeriodicCleanup(): void {
        if (this.cleanupTimer) {
            clearInterval(this.cleanupTimer);
            this.cleanupTimer = null;
            Logger.info('SmartErrorLogger: Stopped periodic cleanup');
        }
    }

    /**
     * 销毁所有记录和定时器
     */
    public static destroy(): void {
        this.stopPeriodicCleanup();
        this.errorRecords.clear();
        Logger.info('SmartErrorLogger: Destroyed all records');
    }
}
