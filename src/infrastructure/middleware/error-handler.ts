/**
 * Error Handler Middleware
 * 
 * Centralized error handling for Express applications.
 */

import { Request, Response, NextFunction } from 'express';
import { StatusCodes } from 'http-status-codes';
import { createErrorResponse } from '@/shared/utils/response-formatter';
import { SmartErrorLogger } from '@/infrastructure/logger/smart-error-logger';
import { getErrorStatusCode, getSafeErrorMessage, AppError } from '@/shared/utils/errors';

/**
 * Extended Request interface with request context
 */
interface ErrorRequest extends Request {
    requestId?: string;
    userId?: string;
}

/**
 * Global error handler middleware
 */
export function errorHandler(
    error: Error,
    req: Request,
    res: Response,
    _next: NextFunction
): void {
    const errorReq = req as ErrorRequest;
    const requestId = errorReq['requestId'];
    const userId = errorReq['userId'];

    // 简化日志上下文构建

    // 只记录未被分类的系统错误，避免重复日志
    if (!(error instanceof AppError)) {
        const logContext = {
            requestId: requestId ?? 'unknown',
            userId: userId ?? 'anonymous',
            method: req.method,
            url: req.url,
        };
        SmartErrorLogger.logError(error, logContext, 'middleware');
    }

    // Prepare error response
    const statusCode = getErrorStatusCode(error);
    const message = getSafeErrorMessage(error);

    const errorResponse = createErrorResponse(
        message,
        'An error occurred while processing your request'
    );

    res.status(statusCode).json(errorResponse);
}

/**
 * 404 Not Found handler
 */
export function notFoundHandler(
    req: Request,
    res: Response
): void {
    // 使用SmartErrorLogger处理404错误，避免过度记录
    const logContext = {
        requestId: 'unknown',
        userId: 'anonymous',
        method: req.method,
        url: req.path,
    };

    const notFoundError = new AppError(
        `Route not found: ${req.method} ${req.path}`,
        StatusCodes.NOT_FOUND,
        true,
        undefined,
        true // 标记为业务异常
    );

    SmartErrorLogger.logError(notFoundError, logContext, 'middleware');

    const errorResponse = createErrorResponse(
        'Route not found',
        `The requested resource ${req.method} ${req.path} was not found`
    );

    res.status(StatusCodes.NOT_FOUND).json(errorResponse);
}
