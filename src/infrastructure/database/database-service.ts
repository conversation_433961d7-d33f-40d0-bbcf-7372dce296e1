/**
 * 数据库服务
 *
 * 统一的数据库连接管理服务，提供 PostgreSQL 连接和 Drizzle ORM 实例。
 * 包含重试机制、事务支持和健康检查功能。
 */

import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { getDatabaseConfig } from '@/infrastructure/config';
import { logInfo, logError } from '@/infrastructure/logger';
import type { BaseService, ServiceHealth } from '@/shared/types/common-types';

type PostgresValue = string | number | boolean | null | Date | Buffer | postgres.Parameter;

class DatabaseService implements BaseService {
    public readonly name = 'database';
    private client: postgres.Sql | null = null;
    private db: ReturnType<typeof drizzle> | null = null;
    private isInitialized = false;
    private readonly initializationPromise: Promise<void>;
    private connectionRetries = 0;
    private readonly maxRetries = 5;
    private readonly retryDelay = 2000; // 2秒

    constructor() {
        // Start initialization but don't await in constructor
        this.initializationPromise = this.initialize();
    }

    /**
     * 确保数据库服务已初始化
     */
    async ensureInitialized(): Promise<void> {
        await this.initializationPromise;
    }

    /**
     * 初始化数据库连接
     */
    async initialize(): Promise<void> {
        if (this.isInitialized) {
            return;
        }
        await this.createConnection();
    }

    /**
     * 创建数据库连接（带重试机制）
     */
    private async createConnection(): Promise<void> {
        try {
            const databaseConfig = getDatabaseConfig();

            logInfo('Creating database connection with config', {
                host: databaseConfig['host'] as string,
                port: databaseConfig['port'] as number,
                database: databaseConfig['database'] as string,
                ssl: databaseConfig['ssl'] as boolean
            });

            // 创建 PostgreSQL 客户端 (优化连接池配置)
            this.client = postgres({
                host: databaseConfig['host'] as string,
                port: databaseConfig['port'] as number,
                database: databaseConfig['database'] as string,
                username: databaseConfig['user'] as string,
                password: databaseConfig['password'] as string,
                ssl: databaseConfig['ssl'] === 'true' ? true : false,

                // 连接池配置 (性能优化)
                max: databaseConfig['max'] as number,                                    // 最大连接数
                idle_timeout: Math.floor((databaseConfig['idleTimeoutMillis'] as number) / 1000),     // 空闲超时(秒)
                connect_timeout: Math.floor((databaseConfig['connectionTimeoutMillis'] as number) / 1000), // 连接超时(秒)
                max_lifetime: Math.floor((databaseConfig['maxLifetimeMillis'] as number) / 1000),     // 连接最大生命周期(秒)

                // 连接管理配置
                prepare: true,                                                          // 启用预处理语句缓存，提升性能
                onnotice: () => { },                                                    // 屏蔽通知

                // 数据转换配置
                transform: {
                    undefined: null,
                },

                // 连接验证配置
                connection: {
                    application_name: 'xui-app-server',                                 // 应用名称标识
                },
            });

            // 创建 Drizzle 实例
            this.db = drizzle(this.client);

            // 测试连接
            await this.testConnection();

            this.isInitialized = true;
            this.connectionRetries = 0;
            logInfo('🐘 Database service initialized successfully', {
                host: databaseConfig['host'] as string,
                port: databaseConfig['port'] as number,
                database: databaseConfig['database'] as string,
                ssl: databaseConfig['ssl'] as boolean,
                // 连接池配置信息
                maxConnections: databaseConfig['max'] as number,
                idleTimeout: Math.floor((databaseConfig['idleTimeoutMillis'] as number) / 1000),
                connectTimeout: Math.floor((databaseConfig['connectionTimeoutMillis'] as number) / 1000),
                maxLifetime: Math.floor((databaseConfig['maxLifetimeMillis'] as number) / 1000),
            });

        } catch (error) {
            logError('❌ Database connection failed:', {}, error as Error);

            if (this.connectionRetries < this.maxRetries) {
                this.connectionRetries++;
                logInfo(
                    `Retrying connection in ${this.retryDelay}ms... (${this.connectionRetries}/${this.maxRetries})`
                );

                // 使用 Promise 来等待重试
                await new Promise<void>((resolve, reject) => {
                    setTimeout(() => {
                        this.createConnection()
                            .then(() => resolve())
                            .catch((retryError) => reject(retryError));
                    }, this.retryDelay);
                });
            } else {
                throw new Error(`Failed to connect to database after ${this.maxRetries} attempts`);
            }
        }
    }

    /**
     * 测试数据库连接
     */
    private async testConnection(): Promise<void> {
        if (!this.client) {
            throw new Error('Database connection not initialized');
        }
        await this.client`SELECT 1 as test`;
    }

    /**
     * 销毁数据库连接
     */
    async destroy(): Promise<void> {
        if (!this.isInitialized || !this.client) {
            return;
        }

        try {
            await this.client.end();
            this.client = null;
            this.db = null;
            this.isInitialized = false;
            logInfo('Database service destroyed successfully');
        } catch (error) {
            logError('Error destroying database service', {}, error as Error);
            throw error;
        }
    }

    /**
     * 数据库服务健康检查
     */
    async healthCheck(): Promise<ServiceHealth> {
        try {
            await this.ensureInitialized();

            if (!this.isInitialized || !this.client) {
                return {
                    status: 'unhealthy',
                    error: 'Database service not initialized',
                };
            }

            const start = Date.now();
            await this.client`SELECT 1`;
            const responseTime = Date.now() - start;

            return {
                status: 'healthy',
                responseTime,
            };
        } catch (error) {
            return {
                status: 'unhealthy',
                error: (error as Error).message,
            };
        }
    }

    /**
     * 获取数据库客户端
     */
    getClient(): postgres.Sql {
        if (!this.isInitialized || !this.client) {
            throw new Error('Database client not initialized. Call ensureInitialized() first.');
        }
        return this.client;
    }

    /**
     * 获取 Drizzle 数据库实例
     */
    getDb(): ReturnType<typeof drizzle> {
        if (!this.isInitialized || !this.db) {
            throw new Error('Database instance not initialized. Call ensureInitialized() first.');
        }
        return this.db;
    }

    /**
     * 检查数据库是否已连接
     */
    isReady(): boolean {
        return this.isInitialized && this.client !== null;
    }

    /**
     * 执行查询并处理错误
     */
    async query<T extends Record<string, unknown>>(
        sql: string,
        params: PostgresValue[] = []
    ): Promise<T[]> {
        if (!this.client || !this.isInitialized) {
            throw new Error('Database not connected');
        }

        try {
            // 将参数化查询转换为模板字面量格式
            const result = await this.client.unsafe(sql, params);
            return result as unknown as T[];
        } catch (error) {
            logError('Database query error:', {}, error as Error);
            throw error;
        }
    }

    /**
     * 执行事务
     */
    async transaction<T>(
        callback: (sql: postgres.Sql) => Promise<T>
    ): Promise<T> {
        if (!this.client || !this.isInitialized) {
            throw new Error('Database not connected');
        }

        return this.client.begin(callback) as Promise<T>;
    }

    /**
     * 获取连接信息 (包含连接池状态)
     */
    getConnectionInfo(): {
        isConnected: boolean;
        host: string;
        port: number;
        database: string;
        ssl: boolean;
        connectionPool: {
            maxConnections: number;
            idleTimeoutSeconds: number;
            connectTimeoutSeconds: number;
            maxLifetimeSeconds: number;
        };
    } {
        // 动态获取数据库配置
        const databaseConfig = getDatabaseConfig();
        return {
            isConnected: this.isInitialized,
            host: databaseConfig['host'] as string,
            port: databaseConfig['port'] as number,
            database: databaseConfig['database'] as string,
            ssl: databaseConfig['ssl'] as boolean,
            connectionPool: {
                maxConnections: databaseConfig['max'] as number,
                idleTimeoutSeconds: Math.floor((databaseConfig['idleTimeoutMillis'] as number) / 1000),
                connectTimeoutSeconds: Math.floor((databaseConfig['connectionTimeoutMillis'] as number) / 1000),
                maxLifetimeSeconds: Math.floor((databaseConfig['maxLifetimeMillis'] as number) / 1000),
            },
        };
    }

    /**
     * 获取连接池统计信息 (用于监控)
     */
    getPoolStats(): {
        maxConnections: number;
        configuration: {
            idleTimeoutSeconds: number;
            connectTimeoutSeconds: number;
            maxLifetimeSeconds: number;
            prepareDisabled: boolean;
        };
    } {
        const databaseConfig = getDatabaseConfig();
        return {
            maxConnections: databaseConfig['max'] as number,
            configuration: {
                idleTimeoutSeconds: Math.floor((databaseConfig['idleTimeoutMillis'] as number) / 1000),
                connectTimeoutSeconds: Math.floor((databaseConfig['connectionTimeoutMillis'] as number) / 1000),
                maxLifetimeSeconds: Math.floor((databaseConfig['maxLifetimeMillis'] as number) / 1000),
                prepareDisabled: true, // 我们禁用了预处理语句缓存
            },
        };
    }
}

// 导出单例实例
export const databaseService = new DatabaseService(); 