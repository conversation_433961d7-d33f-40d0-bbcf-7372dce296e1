/**
 * Session 数据表模式定义
 *
 * 用于存储用户会话的相关信息，包括会话标题、所属用户、创建时间等
 * 支持A2U协议中的会话管理功能
 */
import { InferSelectModel, InferInsertModel, sql } from 'drizzle-orm';
import { jsonb, pgTable, text, timestamp, uuid, varchar } from 'drizzle-orm/pg-core';
import { agent } from './agent';

/**
 * Session 数据表
 * 存储用户与Agent交互会话的基本信息
 */
export const session = pgTable('session', {
    /** 会话的唯一标识符，主键，自动生成UUID */
    id: uuid('id').primaryKey().notNull().defaultRandom(),

    /** 外部会话ID，任意字符串格式，如果不传则自动生成UUID */
    externalId: text('external_id').notNull().unique().default(sql`gen_random_uuid()`),

    /** 会话创建时间戳，默认为当前时间 */
    createdAt: timestamp('created_at').notNull().defaultNow(),

    /** 会话更新时间戳，默认为当前时间 */
    updatedAt: timestamp('updated_at').notNull().defaultNow(),

    /** 会话标题，用于在用户界面中显示 */
    title: text('title').notNull(),

    /** 会话所属用户ID，关联用户身份 */
    userId: varchar('user_id', { length: 255 }).notNull(),

    /** 会话关联的Agent ID，外键引用 agent 表的 id 字段 */
    agentId: uuid('agent_id')
        .notNull()
        .references(() => agent.id, { onDelete: 'cascade' }),

    /** 会话元数据，存储JSON格式的扩展信息 */
    metadata: jsonb('metadata').$type<Record<string, unknown>>(),

    /** 任务ID，用于关联外部任务系统 */
    taskId: text('task_id'),

    /** 任务状态，包含submitted、input-required、auth-required三种状态 */
    taskState: varchar('task_state', { enum: ['submitted', 'input-required', 'auth-required'] }),
});

/**
 * Session 查询类型定义
 * 根据数据表模式自动推断的 TypeScript 类型，用于查询操作
 */
export type Session = InferSelectModel<typeof session>;

/**
 * Session 插入类型定义
 * 根据数据表模式自动推断的 TypeScript 类型，用于插入操作
 */
export type SessionInsert = InferInsertModel<typeof session>;
