import { pgTable, text, uuid, timestamp, integer, boolean, json } from 'drizzle-orm/pg-core';

export const agent = pgTable('agent', {
    id: uuid('id').primaryKey().defaultRandom(),
    name: text('name').notNull(), // Agent名称
    description: text('description'), // Agent描述（可选）
    avatar: text('avatar').notNull(), // Agent头像
    cardUrl: text('card_url').notNull(), // Agent Card的请求地址，用于获取Agent Card信息
    type: integer('type').notNull(), // Agent的类型，1：创作协作类 2：沉浸式教学类
    group: integer('group').notNull().default(1), // Agent分组：1: AI 老师 2: AI 对练 3: Leader Mate
    target: integer('target').notNull(), // 面向用户的范围，二进制位表示："01": 学员 "10"：管理员
    status: integer('status').notNull().default(1), // Agent的状态：0: 已删除 1：正常 2：下架
    umdUrl: text('umd_url'), // plugin umd的地址（可选）
    isSingleSession: boolean('is_single_session').notNull().default(false), // 是否单会话模式，默认false（多会话）
    extraInfo: json('extra_info'), // 额外信息，存储JSON数据（可选）
    userId: text('user_id').notNull(), // 用户ID
    createdAt: timestamp('created_at').defaultNow().notNull(), // 创建时间
    updatedAt: timestamp('updated_at').defaultNow().notNull(), // 更新时间
});

export type Agent = typeof agent.$inferSelect;
export type AgentInsert = typeof agent.$inferInsert;

// Recent session type for agent responses
export type RecentSession = {
    id: string;
    title: string;
    createdAt: Date;
    updatedAt: Date;
    metadata: string | null;
};

// Agent with recent sessions type
export type AgentWithRecentSessions = Agent & {
    recentSessions: RecentSession[];
};

// Agent 类型枚举
export const AgentType = {
    CREATIVE_COLLABORATION: 1, // 创作协作类
    IMMERSIVE_TEACHING: 2,     // 沉浸式教学类
} as const;

export type AgentTypeValue = typeof AgentType[keyof typeof AgentType];

// Agent 分组枚举
export const AgentGroup = {
    AI_TEACHER: 1,       // AI 老师
    AI_PRACTICE: 2,      // AI 对练
    LEADER_MATE: 3,      // Leader Mate
} as const;

export type AgentGroupValue = typeof AgentGroup[keyof typeof AgentGroup];

// Agent 目标用户枚举（二进制位表示）
export const AgentTarget = {
    STUDENT: 1,     // "01": 学员
    ADMIN: 2,       // "10": 管理员
    BOTH: 3,        // "11": 学员和管理员
} as const;

export type AgentTargetValue = typeof AgentTarget[keyof typeof AgentTarget];

// Agent 状态枚举
export const AgentStatus = {
    DELETED: 0,     // 已删除
    ACTIVE: 1,      // 正常
    INACTIVE: 2,    // 下架
} as const;

export type AgentStatusValue = typeof AgentStatus[keyof typeof AgentStatus];

// Agent 会话模式枚举
export const AgentSessionMode = {
    MULTI_SESSION: false,   // 多会话模式（默认）
    SINGLE_SESSION: true,   // 单会话模式
} as const;

export type AgentSessionModeValue = typeof AgentSessionMode[keyof typeof AgentSessionMode];

// 辅助函数：检查目标用户权限
export const hasTargetPermission = (target: number, userType: AgentTargetValue): boolean => {
    return (target & userType) !== 0;
};

// 辅助函数：获取类型描述
export const getAgentTypeDescription = (type: AgentTypeValue): string => {
    switch (type) {
        case AgentType.CREATIVE_COLLABORATION:
            return '创作协作类';
        case AgentType.IMMERSIVE_TEACHING:
            return '沉浸式教学类';
        default:
            return '未知类型';
    }
};

// 辅助函数：获取分组描述
export const getAgentGroupDescription = (group: AgentGroupValue): string => {
    switch (group) {
        case AgentGroup.AI_TEACHER:
            return 'AI 老师';
        case AgentGroup.AI_PRACTICE:
            return 'AI 对练';
        case AgentGroup.LEADER_MATE:
            return 'Leader Mate';
        default:
            return '未分组';
    }
};

// 辅助函数：获取状态描述
export const getAgentStatusDescription = (status: AgentStatusValue): string => {
    switch (status) {
        case AgentStatus.DELETED:
            return '已删除';
        case AgentStatus.ACTIVE:
            return '正常';
        case AgentStatus.INACTIVE:
            return '下架';
        default:
            return '未知状态';
    }
};

// 辅助函数：获取会话模式描述
export const getAgentSessionModeDescription = (isSingleSession: boolean): string => {
    return isSingleSession ? '单会话模式' : '多会话模式';
};

// 辅助函数：验证额外信息JSON格式
export const validateExtraInfo = (extraInfo: unknown): boolean => {
    if (extraInfo === null || extraInfo === undefined) {
        return true; // 允许为空
    }

    try {
        // 如果是字符串，尝试解析
        if (typeof extraInfo === 'string') {
            JSON.parse(extraInfo);
            return true;
        }

        // 如果是对象，检查是否可以序列化
        if (typeof extraInfo === 'object') {
            JSON.stringify(extraInfo);
            return true;
        }

        return false;
    } catch {
        return false;
    }
};
