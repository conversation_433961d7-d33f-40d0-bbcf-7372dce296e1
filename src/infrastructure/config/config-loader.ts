/* eslint-disable no-console */
/**
 * 配置加载器
 * 
 * 负责按正确顺序加载环境变量和 Nacos 配置，避免循环依赖
 */

import { config, parse as parseDotenv } from 'dotenv';
import { NacosConfigClient } from 'nacos';

// 基础环境变量接口
interface BaseEnvironment {
    NACOS_ENABLED: boolean;
    NACOS_SERVER_ADDR?: string | undefined;
    NACOS_NAMESPACE?: string | undefined;
    NACOS_USERNAME?: string | undefined;
    NACOS_PASSWORD?: string | undefined;
}

// 完整配置接口
interface ApplicationConfig {
    // 基础配置
    HOST: string;
    PORT: number;

    // 数据库配置
    DB_HOST: string;
    DB_PORT: number;
    DB_NAME: string;
    DB_USER: string;
    DB_PASSWORD: string;
    DB_SSL: boolean;
    DB_MAX_CONNECTIONS: number;
    DB_IDLE_TIMEOUT: number;
    DB_CONNECTION_TIMEOUT: number;

    // 日志配置
    LOG_LEVEL: string;
    LOG_FORMAT: string;
    LOG_DIR: string;

    // CORS 配置
    CORS_ORIGIN: string | string[];
    CORS_CREDENTIALS: boolean;

    // 其他配置...
    [key: string]: unknown;
}

class ConfigLoader {
    private config: ApplicationConfig | null = null;
    private nacosClient: NacosConfigClient | null = null;

    /**
     * 加载基础环境变量
     */
    private loadBaseEnvironment(): BaseEnvironment {
        // 加载 .env 文件
        config();

        return {
            NACOS_ENABLED: process.env['nacos.enabled'] === 'true',
            NACOS_SERVER_ADDR: process.env['nacos.url'],
            NACOS_NAMESPACE: process.env['nacos.namespace'],
            NACOS_USERNAME: process.env['nacos.rw.username'],
            NACOS_PASSWORD: process.env['nacos.rw.password']
        };
    }

    /**
     * 从 Nacos 加载配置
     */
    private async loadNacosConfig(baseEnv: BaseEnvironment): Promise<Record<string, unknown>> {
        if (!baseEnv.NACOS_ENABLED) {
            console.log('Nacos is disabled, skipping Nacos configuration loading');
            return {};
        }

        if (
            baseEnv.NACOS_SERVER_ADDR === undefined ||
            baseEnv.NACOS_NAMESPACE === undefined ||
            baseEnv.NACOS_USERNAME === undefined ||
            baseEnv.NACOS_PASSWORD === undefined
        ) {
            console.warn('Nacos configuration incomplete, skipping Nacos loading');
            throw new Error('Nacos configuration incomplete');
        }

        try {
            console.log('Loading configuration from Nacos...');

            this.nacosClient = new NacosConfigClient({
                serverAddr: baseEnv.NACOS_SERVER_ADDR,
                namespace: baseEnv.NACOS_NAMESPACE || 'PUBLIC',
                username: baseEnv.NACOS_USERNAME,
                password: baseEnv.NACOS_PASSWORD
            });

            const nacosConfig = await this.nacosClient.getConfig(
                'xuiappserver.properties',
                'DEFAULT_GROUP'
            );

            if (!nacosConfig) {
                console.warn('No configuration found in Nacos');
                throw new Error('No configuration found in Nacos');
            }

            // 解析配置（支持 JSON 和 properties 格式）
            let parsedConfig: Record<string, unknown> = {};

            // 尝试解析为 properties 格式
            parsedConfig = this.parsePropertiesFormat(nacosConfig);
            console.log('Nacos configuration loaded as properties');

            console.log(`Loaded ${Object.keys(parsedConfig).length} configuration items from Nacos`);
            // 打印解析后的配置
            console.log('Parsed Nacos config:', parsedConfig);

            return parsedConfig;

        } catch (error) {
            console.error('Failed to load Nacos configuration:', error);
            throw new Error(
                `Nacos configuration loading failed: ${error instanceof Error ? error.message : 'Unknown error'}`
            );
        }
    }

    /**
     * 解析 properties 格式的配置
     */
    private parsePropertiesFormat(configContent: string): Record<string, string> {
        try {
            // 使用dotenv的parse方法来解析配置内容
            const parsed = parseDotenv(configContent);

            // 过滤掉空值
            const config: Record<string, string> = {};
            Object.keys(parsed).forEach(key => {
                const value = parsed[key];
                if (value !== undefined && value.trim() !== '') {
                    config[key] = value;
                }
            });

            return config;
        } catch (error) {
            console.error('Failed to parse properties config:', {}, error as Error);
            // 如果dotenv解析失败，返回空对象
            throw error;
        }
    }

    
    /**
     * 加载完整配置
     */
    async loadConfiguration(): Promise<ApplicationConfig> {
        if (this.config) {
            return this.config;
        }

        console.log('Starting configuration loading...');

        // 1. 加载基础环境变量
        const baseEnv = this.loadBaseEnvironment();
        console.log(`Base environment loaded: ${baseEnv}`);

        // 2. 根据配置决定是否加载 Nacos
        if(baseEnv.NACOS_ENABLED) {
            const nacosConfig = await this.loadNacosConfig(baseEnv);
            this.config = nacosConfig as ApplicationConfig;
            return this.config;
        } else {
            console.log('Nacos configuration disabled, skipping...');
        }
        

        // 3. 使用env配置
        this.config = process.env as ApplicationConfig;

        console.log('Configuration loading completed');
        return this.config;
    }

    /**
     * 获取当前配置
     */
    getConfiguration(): ApplicationConfig {
        if (!this.config) {
            throw new Error('Configuration not loaded. Call loadConfiguration() first.');
        }
        return this.config;
    }

    close(): Promise<void> {
        if (this.nacosClient) {
            this.nacosClient.close();
        }
        return Promise.resolve();
    }
}

// 导出单例实例
export const configLoader = new ConfigLoader();
export type { ApplicationConfig };
