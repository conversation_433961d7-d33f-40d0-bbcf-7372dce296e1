/**
 * 消息路由
 * 
 * 使用现代模块化架构的消息管理 RESTful API 路由。
 */

import { Router, type IRouter } from 'express';
import { container } from 'tsyringe';
import { TYPES } from '@/shared/constants';
import type { MessageController } from '@/modules/message/controllers';
import {
    validateGetMessage,
    validateDeleteMessage,
    validateSessionMessages,
    validateUpdateMessage,
    validateExportSessionCSV
} from '@/modules/message/validators';
import { validateUserId } from '@/infrastructure/middleware/auth';

const router: IRouter = Router({ strict: true });
const messageController = container.resolve<MessageController>(TYPES.MessageController);

/**
 * @openapi
 * tags:
 *   name: 消息
 *   description: 消息管理端点
 */

/**
 * @openapi
 * /api/message/session/{sessionId}:
 *   get:
 *     summary: 获取会话消息
 *     description: 使用基于游标的分页方式，检索指定会话的消息列表。
 *     tags: [消息]
 *     security:
 *       - userIdHeader: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: 要检索消息的会话ID
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *         description: 返回消息的最大数量
 *       - in: query
 *         name: cursor
 *         schema:
 *           type: string
 *           format: uuid
 *         description: 用于分页的游标，即从哪条消息的 `dbId` 开始返回结果
 *       - in: query
 *         name: tags
 *         schema:
 *           type: array
 *           items:
 *             type: string
 *         style: form
 *         explode: true
 *         description: 用于筛选消息的标签数组
 *       - in: query
 *         name: startTime
 *         schema:
 *           type: string
 *           example: '2024-01-01 00:00:00'
 *         description: '筛选消息的开始时间 (格式: YYYY-MM-DD HH:mm:ss)。如果提供了 startTime 但未提供 endTime，则 endTime 默认为当前时间。'
 *       - in: query
 *         name: endTime
 *         schema:
 *           type: string
 *           example: '2024-01-01 23:59:59'
 *         description: '筛选消息的结束时间 (格式: YYYY-MM-DD HH:mm:ss)。必须在提供 startTime 后才能提供 endTime，且不能早于 startTime。'
 *     responses:
 *       '200':
 *         description: 成功返回消息列表
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/MessageListResponse'
 *       '400':
 *         $ref: '#/components/responses/BadRequestError'
 *       '401':
 *         $ref: '#/components/responses/UnauthorizedError'
 *       '404':
 *         $ref: '#/components/responses/NotFoundError'
 */
router.get(
    '/session/:sessionId',
    validateUserId,
    ...validateSessionMessages,
    messageController.getSessionMessages
);

/**
 * @openapi
 * /api/message/session/{sessionId}/export/csv:
 *   get:
 *     summary: 导出会话消息为CSV格式
 *     description: 根据sessionId全量导出消息记录，返回CSV格式文件。不进行用户ID过滤，导出该会话的所有消息。
 *     tags: [消息]
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: 要导出消息的会话ID
 *     responses:
 *       '200':
 *         description: 成功返回CSV文件
 *         content:
 *           text/csv:
 *             schema:
 *               type: string
 *               format: binary
 *         headers:
 *           Content-Disposition:
 *             description: 文件下载头
 *             schema:
 *               type: string
 *               example: 'attachment; filename="messages_sessionId_2024-01-01.csv"'
 *       '404':
 *         $ref: '#/components/responses/NotFoundError'
 */
router.get(
    '/export/:sessionId',
    ...validateExportSessionCSV,
    messageController.exportSessionMessagesCSV
);

/**
 * @openapi
 * /api/message/{dbId}:
 *   get:
 *     summary: 通过数据库ID获取单条消息
 *     description: 通过其唯一的数据库标识符检索单条消息。
 *     tags: [消息]
 *     security:
 *       - userIdHeader: []
 *     parameters:
 *       - in: path
 *         name: dbId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: 要检索的消息的唯一数据库ID
 *     responses:
 *       '200':
 *         description: 成功返回消息对象
 *         content:
 *           application/json:
 *             schema:
 *                $ref: '#/components/schemas/Message'
 *       '401':
 *         $ref: '#/components/responses/UnauthorizedError'
 *       '404':
 *         $ref: '#/components/responses/NotFoundError'
 *   put:
 *     summary: 更新消息
 *     description: 通过其唯一的数据库标识符更新单条消息的内容或属性。
 *     tags: [消息]
 *     security:
 *       - userIdHeader: []
 *     parameters:
 *       - in: path
 *         name: dbId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: 要更新的消息的唯一数据库ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               sessionId:
 *                 type: string
 *                 format: uuid
 *                 description: 消息所属的会话ID
 *               role:
 *                 type: string
 *                 enum: [user, assistant]
 *                 description: 消息角色
 *               content:
 *                 type: array
 *                 minItems: 1
 *                 description: 消息内容数组
 *                 items:
 *                   type: object
 *                   required: [type]
 *                   properties:
 *                     type:
 *                       type: string
 *                       enum: [text, file, data]
 *                       description: 内容类型
 *                     text:
 *                       type: string
 *                       description: 文本内容（当type为text时）
 *                     file:
 *                       type: object
 *                       description: 文件内容（当type为file时）
 *                       properties:
 *                         bytes:
 *                           type: string
 *                           description: Base64编码的文件内容
 *                         uri:
 *                           type: string
 *                           description: 文件URI
 *                         metadata:
 *                           type: object
 *                           additionalProperties: true
 *                           description: 文件元数据
 *                     data:
 *                       oneOf:
 *                         - type: object
 *                           additionalProperties: true
 *                         - type: string
 *                       description: 结构化数据内容（当type为data时）
 *                     metadata:
 *                       type: object
 *                       additionalProperties: true
 *                       description: 内容元数据
 *               sender:
 *                 type: object
 *                 nullable: true
 *                 description: 消息发送者信息
 *                 properties:
 *                   id:
 *                     type: string
 *                     description: 发送者ID
 *                   name:
 *                     type: string
 *                     description: 发送者名称
 *                   avatar:
 *                     type: string
 *                     description: 发送者头像URL
 *                   type:
 *                     type: string
 *                     description: 发送者类型
 *               extendedData:
 *                 type: object
 *                 additionalProperties: true
 *                 description: 扩展数据
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: 消息标签
 *     responses:
 *       '200':
 *         description: 消息更新成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Message'
 *       '400':
 *         $ref: '#/components/responses/BadRequestError'
 *       '401':
 *         $ref: '#/components/responses/UnauthorizedError'
 *       '404':
 *         $ref: '#/components/responses/NotFoundError'
 *   delete:
 *     summary: 通过数据库ID删除消息
 *     description: 通过其唯一的数据库标识符删除单条消息。
 *     tags: [消息]
 *     security:
 *       - userIdHeader: []
 *     parameters:
 *       - in: path
 *         name: dbId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: 要删除的消息的唯一数据库ID
 *     responses:
 *       '200':
 *         description: 消息删除成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *       '401':
 *         $ref: '#/components/responses/UnauthorizedError'
 *       '404':
 *         $ref: '#/components/responses/NotFoundError'
 */
router.get(
    '/:dbId',
    validateUserId,
    validateGetMessage,
    messageController.getMessageById
);

router.put(
    '/:dbId',
    validateUserId,
    validateUpdateMessage,
    messageController.updateMessage
);
router.delete(
    '/:dbId',
    validateUserId,
    validateDeleteMessage,
    messageController.deleteMessage
);

export default router;
