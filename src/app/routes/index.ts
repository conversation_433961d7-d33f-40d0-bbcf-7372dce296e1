import { Router, type IRouter } from 'express';
import agentRoutes from './agent';
import messageRoutes from './message';
import sessionRoutes from './session';
import healthRoutes from './health';
import userStorageRoutes from './user-storage';

const router: IRouter = Router({ strict: true });

/**
 * @openapi
 * /api/agent:
 *   tags:
 *     - Agent
 *   description: Agent 管理接口
 */
router.use('/agent', agentRoutes);

/**
 * @openapi
 * /api/message:
 *   tags:
 *     - Message
 *   description: 消息管理接口
 */
router.use('/message', messageRoutes);

/**
 * @openapi
 * /api/session:
 *   tags:
 *     - Session
 *   description: 会话管理接口
 */
router.use('/session', sessionRoutes);

/**
 * @openapi
 * /api/user-storage:
 *   tags:
 *     - User Storage
 *   description: 用户存储接口
 */
router.use('/user-storage', userStorageRoutes);

export { healthRoutes };
export default router;
