/**
 * 应用程序引导
 * 
 * 此文件配置Express应用程序所需的所有中间件、
 * 安全设置和路由。它将应用程序配置
 * 与服务器启动逻辑分离。
 */

import express, { type Express } from 'express';
import helmet from 'helmet';
import cors from 'cors';
import compression from 'compression';
import morgan from 'morgan';
// import rateLimit from 'express-rate-limit';

// import { getCorsConfig, getSecurityConfig } from '@/infrastructure/config';
import { getCorsConfig } from '@/infrastructure/config';
import { logStream } from '@/infrastructure/logger';
import {
    requestIdMiddleware,
    errorHandler,
    notFoundHandler,
} from '@/infrastructure/middleware';
import { setupSwagger } from '@/infrastructure/swagger';

/**
 * 创建和配置Express应用程序
 */
export const createApp = (): Express => {
    const app: Express = express();
    const corsConfig = getCorsConfig();

    // 信任代理（如果在负载均衡器后面）
    app.set('trust proxy', 1);

    // 安全中间件
    app.use(
        helmet({
            crossOriginOpenerPolicy: false,
            crossOriginEmbedderPolicy: false,
            originAgentCluster: false, // 禁用Origin-Agent-Cluster头部
            contentSecurityPolicy: {
                directives: {
                    defaultSrc: ["'self'"],
                    styleSrc: ["'self'", "'unsafe-inline'"],
                    scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"], // Swagger UI需要
                    imgSrc: ["'self'", 'data:', 'https:'],
                    connectSrc: ["'self'"], // API调用
                },
            },
            hsts: corsConfig['HSTS_ENABLED'] === 'true' ? {
                maxAge: parseInt(String(corsConfig['HSTS_MAX_AGE'] ?? '').trim() || '31536000', 10),
                includeSubDomains: true,
                preload: true,
            } : false,
        }),
    );

    // CORS配置
    app.use(
        cors({
            origin: corsConfig['origin'] as string,
            credentials: corsConfig['credentials'] as boolean,
            methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
            allowedHeaders: [
                'Origin',
                'X-Requested-With',
                'Content-Type',
                'Accept',
                'Authorization',
                'Cache-Control',
                'X-Request-ID',
                'userid',
                'token',
                'user',
            ],
            exposedHeaders: ['X-Request-ID'],
            maxAge: 86400,
        }),
    );

    // 响应压缩
    app.use(compression());

    // 请求体解析
    app.use(express.json({ limit: '10mb' }));
    app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // 请求ID中间件
    app.use(requestIdMiddleware);

    // HTTP请求日志记录
    app.use(morgan('short', {
        stream: logStream,
        skip: (req) => {
            // 跳过健康检查和静态资源
            return req.url.startsWith('/health') ||
                req.url.startsWith('/serverstatus') ||
                req.url.startsWith('/ping') ||
                req.url.startsWith('/live') ||
                req.url.startsWith('/ready') ||
                req.url.startsWith('/api-docs')
        },
    }));

    // 速率限制
    // const limiter = rateLimit({
    //     windowMs: securityConfig['rateLimitWindowMs'] as number,
    //     max: securityConfig['rateLimitMaxRequests'] as number,
    //     message: {
    //         success: false,
    //         message: 'Too many requests from this IP, please try again later',
    //         error: 'Rate limit exceeded',
    //         timestamp: getDateTime(new Date()),
    //     },
    //     standardHeaders: true,
    //     legacyHeaders: false,
    // });
    // app.use('/api', limiter);

    return app;
};

/**
 * 配置路由 - 必须在容器初始化后调用
 */
export const configureRoutes = async (app: Express): Promise<void> => {
    // 动态导入路由，确保容器已经初始化
    const { default: apiRoutes, healthRoutes } = await import('./routes');

    // 挂载健康检查路由到根路径
    app.use('/', healthRoutes);

    // 挂载所有其他API路由到 /api 路径
    app.use('/api', apiRoutes);

    // 配置Swagger文档
    setupSwagger(app);

    // 404处理器
    app.use(notFoundHandler);

    // 全局错误处理器
    app.use(errorHandler);
}; 