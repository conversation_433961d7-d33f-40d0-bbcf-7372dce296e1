/**
 * 共享类型
 * 
 * 所有共享类型定义的中央导出。
 */

// API类型
export type {
    AuthenticatedRequest,
    TypedResponse,
    PaginationQuery,
    PaginatedRequest,
    ApiResponse,
    PaginationMetadata,
    PaginatedResponse,
    ErrorResponse,
    HealthResponse,
    ServiceHealth,
    QueryParams,
    FileUploadInfo,
    BatchOperationResult,
    RateLimitInfo,
    RequestContext,
    ValidationErrorDetail,
    ValidationErrorResponse,
    CursorPaginatedResponse,
    ApiError,
} from './api-types';

// 通用类型
export type {
    Environment,
    LogContext,
} from './common-types';

// 数据库类型
export type {
    DatabaseConfig,
    QueryResult,
} from './database-types';

// Langfuse类型
export type {
    JsonValue,
    JsonObject,
    JsonArray,
    LangfuseTrace,
    LangfuseSpan,
    LangfuseGeneration,
    LangfuseTraceMetadata,
    LangfuseGenerationConfig,
    LangfuseScoreConfig,
} from './langfuse-types';

// 健康检查类型
export type {
    HealthCheckDto,
    ServiceHealthDto,
    MemoryUsageDto,
    CpuUsageDto,
    SimpleHealthDto,
    ReadinessDto,
    LivenessDto,
    HealthStatusDto,
} from '@/modules/health/dto'; 