
import type { LogContext } from '@/shared/types';









/**
 * Safe JSON parsing
 */
export const safeJsonParse = <T = unknown>(jsonString: string, defaultValue: T): T => {
    try {
        return JSON.parse(jsonString) as T;
    } catch {
        return defaultValue;
    }
};



/**
 * Array chunking
 */
export const chunk = <T>(array: T[], size: number): T[][] => {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
        chunks.push(array.slice(i, i + size));
    }
    return chunks;
};

/**
 * Safely create LogContext with only defined values
 * This handles exactOptionalPropertyTypes: true in TypeScript config
 */
export function createLogContext(context: {
    requestId?: string | undefined;
    userId?: string | undefined;
    method?: string | undefined;
    url?: string | undefined;
    statusCode?: number | undefined;
    duration?: number | undefined;
    userAgent?: string | undefined;
    ip?: string | undefined;
    host?: string | undefined;
    port?: number | undefined;
    database?: string | undefined;
    processId?: number | undefined;
    [key: string]: unknown;
}): LogContext {
    const logContext: LogContext = {};

    // Only add properties that have defined values
    if (context.requestId !== undefined) {
        logContext.requestId = context.requestId;
    }

    if (context.userId !== undefined) {
        logContext.userId = context.userId;
    }

    if (context.method !== undefined) {
        logContext.method = context.method;
    }

    if (context.url !== undefined) {
        logContext.url = context.url;
    }

    if (context.statusCode !== undefined) {
        logContext.statusCode = context.statusCode;
    }

    if (context.duration !== undefined) {
        logContext.duration = context.duration;
    }

    if (context.userAgent !== undefined) {
        logContext.userAgent = context.userAgent;
    }

    if (context.ip !== undefined) {
        logContext.ip = context.ip;
    }

    if (context.host !== undefined) {
        logContext.host = context.host;
    }

    if (context.port !== undefined) {
        logContext.port = context.port;
    }

    if (context.database !== undefined) {
        logContext.database = context.database;
    }

    if (context.processId !== undefined) {
        logContext.processId = context.processId;
    }

    // Add any other properties
    Object.keys(context).forEach(key => {
        if (!['requestId', 'userId', 'method', 'url', 'statusCode', 'duration',
            'userAgent', 'ip', 'host', 'port', 'database', 'processId'].includes(key)) {
            if (context[key] !== undefined) {
                logContext[key] = context[key];
            }
        }
    });

    return logContext;
}

/**
 * Safe logging functions that handle exactOptionalPropertyTypes
 */



// Export A2A client
export { XuiA2AClient } from './xui-a2a-client';

/**
 * Shared Utilities
 * 
 * Centralized exports for all utility functions and classes.
 */

// Base components for controllers and services
export * from './base-controller';

// Utility functions
export * from './crypto';
export * from './errors';
export * from './message-converter';
export * from './response-formatter';
export * from './time';
export * from './validator-helpers';
