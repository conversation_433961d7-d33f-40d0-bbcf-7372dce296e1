/**
 * Error Handling Utilities
 *
 * Custom error classes and error handling utilities.
 */
import { StatusCodes } from 'http-status-codes';

/**
 * Base application error class
 */
export class AppError extends Error {
    public readonly isOperational: boolean;
    public readonly statusCode: number;
    public readonly details?: unknown;
    public readonly isBusinessError: boolean; // 简单标记：是否为业务异常

    constructor(
        message: string,
        statusCode = StatusCodes.INTERNAL_SERVER_ERROR,
        isOperational = true,
        details?: unknown,
        isBusinessError = false
    ) {
        super(message);

        this.name = this.constructor.name;
        this.statusCode = statusCode;
        this.isOperational = isOperational;
        this.details = details;
        this.isBusinessError = isBusinessError;

        Error.captureStackTrace(this, this.constructor);
    }
}

/**
 * Validation error
 */
export class ValidationError extends AppError {
    constructor(message: string, details?: unknown) {
        super(message, StatusCodes.BAD_REQUEST, true, details, true); // 标记为业务异常
    }
}

/**
 * Authentication error
 */
export class AuthenticationError extends AppError {
    constructor(message = 'Authentication failed') {
        super(message, StatusCodes.UNAUTHORIZED, true, undefined, true); // 标记为业务异常
    }
}

/**
 * Authorization error
 */
export class AuthorizationError extends AppError {
    constructor(message = 'Access forbidden') {
        super(message, StatusCodes.FORBIDDEN, true);
    }
}

/**
 * Not found error
 */
export class NotFoundError extends AppError {
    constructor(resource = 'Resource', message?: string) {
        const errorMessage = message ?? `${resource} not found`;
        super(errorMessage, StatusCodes.NOT_FOUND, true);
    }
}

/**
 * Resource not found error (for data that doesn't exist but route is valid)
 * This should return 200 with null data, not 404
 */
export class ResourceNotFoundError extends AppError {
    constructor(resource = 'Resource', message?: string) {
        const errorMessage = message ?? `${resource} not found`;
        super(errorMessage, StatusCodes.OK, true, undefined, true); // 标记为业务异常
    }
}

/**
 * Conflict error
 */
export class ConflictError extends AppError {
    constructor(message = 'Resource conflict', details?: unknown) {
        super(message, StatusCodes.CONFLICT, true, details);
    }
}

/**
 * Database error
 */
export class DatabaseError extends AppError {
    public readonly operation?: string;
    public readonly table?: string;
    public readonly originalError?: Error;

    constructor(
        message = 'Database operation failed', 
        details?: unknown,
        operation?: string,
        table?: string,
        originalError?: Error
    ) {
        super(message, StatusCodes.INTERNAL_SERVER_ERROR, true, details);
        if (operation !== undefined) {
            this.operation = operation;
        }
        if (table !== undefined) {
            this.table = table;
        }
        if (originalError !== undefined) {
            this.originalError = originalError;
        }
    }
}

/**
 * External service error
 */
export class ExternalServiceError extends AppError {
    constructor(service: string, message?: string, details?: unknown) {
        const errorMessage = message ?? `${service} service unavailable`;
        super(errorMessage, StatusCodes.SERVICE_UNAVAILABLE, true, details);
    }
}

/**
 * Rate limit error
 */
export class RateLimitError extends AppError {
    constructor(message = 'Rate limit exceeded') {
        super(message, StatusCodes.TOO_MANY_REQUESTS, true);
    }
}

/**
 * Timeout error
 */
export class TimeoutError extends AppError {
    constructor(operation = 'Operation', timeout?: number) {
        const message = timeout !== undefined && timeout > 0
            ? `${operation} timed out after ${timeout}ms`
            : `${operation} timed out`;
        super(message, StatusCodes.REQUEST_TIMEOUT, true);
    }
}

/**
 * Check if error is operational (safe to expose to user)
 */
export function isOperationalError(error: Error): boolean {
    if (error instanceof AppError) {
        return error.isOperational;
    }
    return false;
}

/**
 * Get error status code
 */
export function getErrorStatusCode(error: Error): number {
    if (error instanceof AppError) {
        return error.statusCode;
    }
    
    // Handle Express body-parser errors (JSON parsing, etc.)
    const bodyParserError = error as Error & { 
        statusCode?: number; 
        status?: number; 
        type?: string;
    };
    if (bodyParserError.type === 'entity.parse.failed' || 
        bodyParserError.statusCode === 400 || 
        bodyParserError.status === 400) {
        return StatusCodes.BAD_REQUEST;
    }
    
    // Handle validation errors (e.g., from Zod or custom validators)
    const validationError = error as Error & { code?: string };
    if (validationError.code === 'VALIDATION_ERROR') {
        return StatusCodes.BAD_REQUEST;
    }
    
    // Handle built-in JavaScript error types
    if (error instanceof SyntaxError) {
        return StatusCodes.BAD_REQUEST; // Bad request for syntax errors
    }
    
    if (error instanceof TypeError && error.message.includes('validation')) {
        return StatusCodes.BAD_REQUEST; // Bad request for type validation errors
    }
    
    if (error instanceof RangeError) {
            return StatusCodes.BAD_REQUEST; // Bad request for range errors
    }
    
    // Default to INTERNAL_SERVER_ERROR for unknown errors
    return StatusCodes.INTERNAL_SERVER_ERROR;
}

/**
 * Get safe error message for user
 */
export function getSafeErrorMessage(error: Error): string {
    // Handle Express body-parser JSON errors specifically
    const bodyParserError = error as Error & { 
        type?: string; 
        body?: string;
        statusCode?: number;
    };
    
    if (bodyParserError.type === 'entity.parse.failed') {
        if (error instanceof SyntaxError) {
            // Extract useful information from JSON syntax error
            const match = error.message.match(/at position (\d+)/);
            const position = match ? match[1] : 'unknown';
            return `Invalid JSON format: ${error.message}. Check your request body syntax at position ${position}.`;
        }
        return `Invalid request body format: ${error.message}`;
    }
    
    // In production, only show operational errors to users
    if (isOperationalError(error)) {
        return error.message;
    }
    
    // Handle validation errors (these are usually safe to show)
    const validationError = error as Error & { code?: string };
    if (validationError.code === 'VALIDATION_ERROR') {
        return error.message;
    }
    
    // For non-operational errors in production, return generic message
    return 'An internal server error occurred';
}


/**
 * 转换数据库错误为 DatabaseError
 */
export function convertDatabaseError(
    error: Error,
    operation: string,
    table?: string
): DatabaseError {
    // 解析常见的数据库错误类型
    let message = `Database ${operation} failed`;
    let details: Record<string, unknown> = {};
    
    // PostgreSQL 错误代码解析
    const pgError = error as Error & { 
        code?: string; 
        constraint?: string; 
        detail?: string;
        table?: string;
        column?: string;
    };
    
    if (pgError.code) {
        switch (pgError.code) {
            case '23505': // unique_violation
                message = `Duplicate entry: ${pgError.constraint ?? 'unique constraint violation'}`;
                details = {
                    errorType: 'DUPLICATE_ENTRY',
                    constraint: pgError.constraint,
                    table: pgError.table ?? table,
                };
                break;
            case '23503': // foreign_key_violation
                message = `Foreign key constraint violation: ${pgError.constraint ?? 'reference not found'}`;
                details = {
                    errorType: 'FOREIGN_KEY_VIOLATION',
                    constraint: pgError.constraint,
                    table: pgError.table ?? table,
                };
                break;
            case '23502': // not_null_violation
                message = `Required field missing: ${pgError.column ?? 'field cannot be null'}`;
                details = {
                    errorType: 'NOT_NULL_VIOLATION',
                    column: pgError.column,
                    table: pgError.table ?? table,
                };
                break;
            case '42703': // undefined_column
                message = `Unknown column: ${pgError.detail ?? 'column does not exist'}`;
                details = {
                    errorType: 'UNDEFINED_COLUMN',
                    table: pgError.table ?? table,
                };
                break;
            case '42P01': // undefined_table
                message = `Unknown table: ${table ?? 'table does not exist'}`;
                details = {
                    errorType: 'UNDEFINED_TABLE',
                    table: table,
                };
                break;
            case '08006': // connection_failure
                message = 'Database connection failed';
                details = {
                    errorType: 'CONNECTION_FAILURE',
                };
                break;
            default:
                message = `Database ${operation} failed: ${error.message}`;
                details = {
                    errorType: 'UNKNOWN_DATABASE_ERROR',
                    code: pgError.code,
                    table: pgError.table ?? table,
                };
        }
    } else {
        // 处理 Drizzle ORM 或其他库的错误
        if (error.message.includes('Failed query')) {
            message = `Database query failed during ${operation}`;
            details = {
                errorType: 'QUERY_FAILED',
                table: table,
            };
        } else if (error.message.includes('timeout')) {
            message = `Database ${operation} timed out`;
            details = {
                errorType: 'TIMEOUT',
                table: table,
            };
        } else {
            message = `Database ${operation} failed: ${error.message}`;
            details = {
                errorType: 'UNKNOWN_ERROR',
                table: table,
            };
        }
    }
    
    return new DatabaseError(message, details, operation, table, error);
}