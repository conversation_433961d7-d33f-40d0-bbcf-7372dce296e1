/**
 * A2U和A2A消息格式转换工具
 *
 * 用于在A2U (Agent-to-User) 和 A2A (Agent-to-Agent) 协议之间转换消息格式
 */

import type {
    Message,
    MessageSendParams,
    Task,
    TaskStatusUpdateEvent,
    TaskArtifactUpdateEvent,
    Part,
    Artifact,
} from '@a2a-js/sdk';

import {
    A2UEventType,
    type A2UUserMessage,
    type A2UAssistantMessage,
    type A2UMessageContent,
    type FileWithBytes,
    type FileWithUri,
    type A2USessionStartEvent,
    type A2USessionFinishEvent,
    type A2USessionErrorEvent,
    type A2UMessageStartEvent,
    type A2UPartStartEvent,
    type A2UPartContentEvent,
    type A2UPartEndEvent,
    type A2UMessageEndEvent,
    type A2UPingEvent,
} from '@xui-web-app/a2u';
import { getDateTime } from './time';

// 使用A2A SDK的类型
export type A2AStreamEvent = Task | TaskStatusUpdateEvent | TaskArtifactUpdateEvent | Message;

/**
 * 将A2U消息内容转换为A2A格式的parts
 */
export function convertA2UContentToA2AParts(content: A2UMessageContent[]): Part[] {
    const parts: Part[] = [];

    for (const item of content) {
        switch (item.type) {
            case 'text':
                if (item.extendedData) {
                    parts.push({
                        kind: 'text',
                        text: item.text || '',
                        metadata: item.extendedData,
                    });
                } else {
                    parts.push({
                        kind: 'text',
                        text: item.text || '',
                    });
                }
                break;

            case 'file':
                if ('bytes' in item.file && item.file.bytes.length > 0) {
                    if (item.extendedData) {
                        parts.push({
                            kind: 'file',
                            file: {
                                bytes: item.file.bytes,
                            },
                            metadata: item.extendedData,
                        });
                    } else {
                        parts.push({
                            kind: 'file',
                            file: {
                                bytes: item.file.bytes,
                            },
                        });
                    }
                } else if ('uri' in item.file && item.file.uri.length > 0) {
                    if (item.extendedData) {
                        parts.push({
                            kind: 'file',
                            file: {
                                uri: item.file.uri,
                            },
                            metadata: item.extendedData,
                        });
                    } else {
                        parts.push({
                            kind: 'file',
                            file: {
                                uri: item.file.uri,
                            },
                        });
                    }
                } else {
                    // 如果文件信息不完整，转换为文本
                    if (item.extendedData) {
                        parts.push({
                            kind: 'text',
                            text: JSON.stringify(item),
                            metadata: item.extendedData,
                        });
                    } else {
                        parts.push({
                            kind: 'text',
                            text: JSON.stringify(item),
                        });
                    }
                }
                break;

            // 工具调用相关的内容类型在新版本中已移除

            case 'data':
                // 单个数据对象
                if (item.extendedData) {
                    parts.push({
                        kind: 'data',
                        data: item.data as unknown as Record<string, unknown>,
                        metadata: item.extendedData,
                    });
                } else {
                    parts.push({
                        kind: 'data',
                        data: item.data as unknown as Record<string, unknown>,
                    });
                }
                break;

            default:
                // 默认作为文本处理
                parts.push({
                    kind: 'text',
                    text: JSON.stringify(item),
                });
                break;
        }
    }

    return parts;
}

/**
 * 将A2U消息转换为A2A消息格式
 */
export function convertA2UMessageToA2A(a2uMessage: A2UUserMessage): MessageSendParams {
    const a2aParts = convertA2UContentToA2AParts(a2uMessage.content);

    const a2aMessage: Message = {
        messageId: a2uMessage.id,
        role: 'user', // A2UUserMessage always has role 'user'
        parts: a2aParts,
        kind: 'message',
    };

    return {
        message: a2aMessage,
        configuration: {
            blocking: false, // 使用流式响应
            acceptedOutputModes: ['text/plain', 'application/json'],
        },
    };
}

/**
 * 将A2A的parts转换为A2U消息内容
 */
export function convertA2APartsToA2UContent(parts: Part[]): A2UMessageContent[] {
    return parts.map((part: Part): A2UMessageContent => {

        switch (part.kind) {
            case 'text': {
                const textPart = part;
                const result: A2UMessageContent = {
                    type: 'text',
                    text: textPart.text,
                };
                if (textPart.metadata) {
                    result.extendedData = textPart.metadata as Record<string, unknown>;
                }
                return result;
            }

            case 'file': {
                const filePart = part;
                if ('bytes' in filePart.file && filePart.file.bytes) {
                    const result: A2UMessageContent = {
                        type: 'file',
                        file: {
                            bytes: filePart.file.bytes,
                        } as FileWithBytes,
                    };
                    if (filePart.metadata) {
                        result.extendedData = filePart.metadata as Record<string, unknown>;
                    }
                    return result;
                } else if ('uri' in filePart.file && filePart.file.uri) {
                    const result: A2UMessageContent = {
                        type: 'file',
                        file: {
                            uri: filePart.file.uri,
                        } as FileWithUri,
                    };
                    if (filePart.metadata) {
                        result.extendedData = filePart.metadata as Record<string, unknown>;
                    }
                    return result;
                } else {
                    // 如果文件信息不完整，转换为文本
                    const result: A2UMessageContent = {
                        type: 'text',
                        text: JSON.stringify(part),
                    };
                    if (filePart.metadata) {
                        result.extendedData = filePart.metadata as Record<string, unknown>;
                    }
                    return result;
                }
            }

            case 'data': {
                // 将A2A数据转换为A2U数据格式，每个data part独立转换
                try {
                    const dataPart = part;
                    const result: A2UMessageContent = {
                        type: 'data',
                        data: dataPart.data as unknown as Record<string, unknown>,
                    };
                    if (dataPart.metadata) {
                        result.extendedData = dataPart.metadata as Record<string, unknown>;
                    }
                    return result;
                } catch {
                    // 如果数据格式转换失败，转换为文本
                    const dataPart = part;
                    const result: A2UMessageContent = {
                        type: 'text',
                        text: JSON.stringify(dataPart.data),
                    };
                    if (dataPart.metadata) {
                        result.extendedData = dataPart.metadata as Record<string, unknown>;
                    }
                    return result;
                }
            }

            default:
                // 默认作为文本处理
                return {
                    type: 'text',
                    text: JSON.stringify(part),
                };
        }
    });
}

/**
 * 将A2A消息转换为A2U格式
 * 支持 Message 和 Artifact 两种类型的转换
 */
export function convertA2AMessageToA2U(a2aMessage: Message | Artifact, agentId: string): A2UAssistantMessage {
    // 判断输入类型并获取相应的 ID
    const messageId = 'messageId' in a2aMessage
        ? a2aMessage.messageId
        : a2aMessage.artifactId;

    const baseMessage = {
        id: messageId,
        role: 'assistant' as const,
        timestamp: getDateTime(new Date()),
        content: convertA2APartsToA2UContent(a2aMessage.parts),
        sender: {
            id: agentId,
        },
    };

    // Only include extendedData if metadata exists
    if (a2aMessage.metadata) {
        return {
            ...baseMessage,
            extendedData: a2aMessage.metadata as Record<string, unknown>,
        };
    }

    return baseMessage;
}

/**
 * 创建会话开始事件
 */
export function createSessionStartEvent(
    sessionId: string, 
    traceId: string
): A2USessionStartEvent & { traceId: string } {
    return {
        type: A2UEventType.SESSION_START,
        sessionId,
        traceId: traceId,
        timestamp: getDateTime(new Date()),
    };
}

/**
 * 创建会话完成事件
 */
export function createSessionFinishEvent(sessionId: string): A2USessionFinishEvent {
    return {
        type: A2UEventType.SESSION_END,
        sessionId,
        timestamp: getDateTime(new Date()),
    };
}

/**
 * 创建会话错误事件
 */
export function createSessionErrorEvent(sessionId: string, code: string): A2USessionErrorEvent {
    return {
        type: A2UEventType.SESSION_ERROR,
        sessionId,
        timestamp: getDateTime(new Date()),
        code,
    };
}

/**
 * 创建消息开始事件
 */
export function createMessageStartEvent(
    messageId: string,
    sender: { id: string; name?: string; avatar?: string },
    agentMessageDbId: string
): A2UMessageStartEvent & { agentMessageDbId?: string } {
    return {
        type: A2UEventType.MESSAGE_START,
        timestamp: getDateTime(new Date()),
        messageId,
        sender,
        agentMessageDbId
    };
}

/**
 * 创建消息结束事件
 */
export function createMessageEndEvent(messageId: string): A2UMessageEndEvent {
    return {
        type: A2UEventType.MESSAGE_END,
        timestamp: getDateTime(new Date()),
        messageId,
    };
}

/**
 * 创建Part开始事件
 */
export function createPartStartEvent(
    index: number,
    contentType: 'text' | 'file' | 'data'
): A2UPartStartEvent {
    return {
        index,
        type: A2UEventType.PART_START,
        timestamp: getDateTime(new Date()),
        contentType,
    };
}

/**
 * 创建Part内容事件
 */

export type eventDelta = { type: 'text_delta'; text: string, extendedData?: Record<string, unknown> }
    | { type: 'file_delta'; file: FileWithBytes | FileWithUri, extendedData?: Record<string, unknown> }
    | { type: 'data_delta'; data: string, extendedData?: Record<string, unknown> };

export function createPartContentEvent(
    index: number,
    delta: eventDelta
): Omit<A2UPartContentEvent, 'content'> {
    return {
        index,
        type: A2UEventType.PART_CONTENT,
        timestamp: getDateTime(new Date()),
        delta,
    } as unknown as Omit<A2UPartContentEvent, 'content'>;
}

/**
 * 创建Part结束事件
 */
export function createPartEndEvent(index: number): A2UPartEndEvent {
    return {
        index,
        type: A2UEventType.PART_END,
        timestamp: getDateTime(new Date()),
    };
}

/**
 * 创建保活事件
 */
export function createPingEvent(): A2UPingEvent {
    return {
        type: A2UEventType.PING,
        timestamp: getDateTime(new Date()),
    } as A2UPingEvent;
}