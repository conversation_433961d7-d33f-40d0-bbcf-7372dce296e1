/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */

import { Logger } from '@/infrastructure/logger';
import { A2AClient } from '@a2a-js/sdk/client';
import { type MessageSendParams } from '@a2a-js/sdk';

export class XuiA2AClient extends A2AClient {
    private customHeaders: Record<string, string> = {};
    private readonly requestId: string;

    constructor(agentBaseUrl: string) {
        super(agentBaseUrl);

        // 生成唯一的请求ID用于调试和追踪
        this.requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
    }

    public setHeaders(headers: Record<string, string>): void {
        this.customHeaders = { ...this.customHeaders, ...headers };
    }

    public getHeaders(): Record<string, string> {
        return { ...this.customHeaders };
    }

    public setHeader(key: string, value: string): void {
        this.customHeaders[key] = value;
    }

    /**
     * 重写 sendMessageStream 方法以支持自定义请求头和完全独立的 HTTP 连接
     */
    public override async *sendMessageStream(params: MessageSendParams): AsyncGenerator<any, void, undefined> {
        const agentCard = await this.getAgentCard();

        const capabilities = agentCard.capabilities as { streaming?: boolean } | undefined;
        if (!capabilities || capabilities.streaming !== true) {
            throw new Error("Agent does not support streaming (AgentCard.capabilities.streaming is not true).");
        }

        const endpoint = await (this as any)._getServiceEndpoint();
        const clientRequestId = (this as any).requestIdCounter++;
        const rpcRequest = {
            jsonrpc: "2.0",
            method: "message/stream",
            params,
            id: clientRequestId,
        };

        const headers = {
            "Content-Type": "application/json",
            "Accept": "text/event-stream",
            "X-Request-ID": this.requestId, // 添加请求ID用于追踪
            ...this.customHeaders,
        };

        Logger.info(`[${this.requestId}] Sending request to ${endpoint}`);
        Logger.info(`[${this.requestId}] Headers: ${JSON.stringify(headers)}`);
        Logger.info(`[${this.requestId}] Request body: ${JSON.stringify(rpcRequest)}`);

        const response = await globalThis.fetch(endpoint, {
            method: "POST",
            headers,
            body: JSON.stringify(rpcRequest),
        });

        if (!response.ok) {
            let errorBody = "";
            try {
                errorBody = await response.text();
                const errorJson = JSON.parse(errorBody) as { error?: { message: string; code: string } };

                if (errorJson.error) {
                    const errorMsg =
                        `HTTP error establishing stream for message/stream: ` +
                        `${response.status} ${response.statusText}. ` +
                        `RPC Error: ${errorJson.error.message} (Code: ${errorJson.error.code})`;
                    throw new Error(errorMsg);
                }
            } catch (e) {
                if (e instanceof Error && e.message.startsWith('HTTP error establishing stream')) {
                    throw e;
                }
                const errorMsg = `HTTP error establishing stream for message/stream: ` +
                    `${response.status} ${response.statusText}. Response: ${errorBody || '(empty)'}`;
                throw new Error(errorMsg);
            }
            const errorMsg = `HTTP error establishing stream for message/stream: ` +
                `${response.status} ${response.statusText}`;
            throw new Error(errorMsg);
        }

        const contentType = response.headers.get("Content-Type");
        const isValidContentType = contentType?.startsWith("text/event-stream") ?? false;
        if (isValidContentType !== true) {
            throw new Error("Invalid response Content-Type for SSE stream. Expected 'text/event-stream'.");
        }
        yield* (this as any)._parseA2ASseStream(response, clientRequestId);
    }
}