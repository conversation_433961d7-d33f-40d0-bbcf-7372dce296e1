/**
 * 基础控制器
 *
 * 为所有控制器提供通用功能。
 */

import { type Request, type Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import { createSuccessResponse, createErrorResponse, createPaginatedResponse } from './response-formatter';
import { SmartErrorLogger } from '@/infrastructure/logger/smart-error-logger';
import { Logger } from '@/infrastructure/logger/winston-logger';
import { 
    NotFoundError, 
    ResourceNotFoundError, 
    ValidationError, 
    DatabaseError, 
    ConflictError, 
    AuthenticationError, 
    AuthorizationError, 
    TimeoutError
} from './errors';
import type { 
    LogContext,
    PaginationMetadata,
    AuthenticatedRequest, 
    TypedResponse, 
    PaginatedRequest,
    PaginationQuery,
} from '@/shared/types';

export abstract class BaseController {
    /**
     * 处理异步操作并捕获错误
     */
    protected asyncHandler<T, U extends unknown[]>(
        fn: (req: AuthenticatedRequest, res: TypedResponse<T>, ...args: U) => Promise<void>
    ) {
        return (req: Request, res: Response, ...args: U): Promise<void> => {
            return fn(req as AuthenticatedRequest, res as TypedResponse<T>, ...args).catch((error: Error) => {
                this.handleError(error, req as AuthenticatedRequest, res as TypedResponse<T>, 'Operation failed');
            });
        };
    }

    /**
     * 为所有控制器提供统一的错误处理
     */
    protected handleError<T>(
        error: Error,
        req: AuthenticatedRequest,
        res: TypedResponse<T>,
        defaultMessage: string
    ): void {
        const logContext: LogContext = {
            requestId: req.requestId ?? 'unknown',
            userId: req.user?.id ?? 'anonymous',
            method: req.method,
            url: req.path,
        };

        // 使用智能日志记录器，避免重复日志
        SmartErrorLogger.logError(error, logContext, 'controller');

        if (error instanceof NotFoundError) {
            (res as TypedResponse<unknown>).status(StatusCodes.NOT_FOUND).json(
                createErrorResponse(
                    error.message,
                    'Resource not found'
                )
            );
            return;
        }

        if (error instanceof ResourceNotFoundError) {
            (res as TypedResponse<unknown>).status(StatusCodes.OK).json(
                createSuccessResponse(
                    null,
                    error.message
                )
            );
            return;
        }

        if (error instanceof ValidationError) {
            (res as TypedResponse<unknown>).status(StatusCodes.BAD_REQUEST).json(
                createErrorResponse(
                    error.message,
                    'Validation failed'
                )
            );
            return;
        }

        if (error instanceof ConflictError) {
            (res as TypedResponse<unknown>).status(StatusCodes.CONFLICT).json(
                createErrorResponse(
                    error.message,
                    'Resource conflict'
                )
            );
            return;
        }

        if (error instanceof AuthorizationError) {
            (res as TypedResponse<unknown>).status(StatusCodes.FORBIDDEN).json(
                createErrorResponse(
                    error.message,
                    'Access forbidden'
                )
            );
            return;
        }

        if (error instanceof AuthenticationError) {
            (res as TypedResponse<unknown>).status(StatusCodes.UNAUTHORIZED).json(
                createErrorResponse(
                    error.message,
                    'Authentication failed'
                )
            );
            return;
        }

        if (error instanceof TimeoutError) {
            (res as TypedResponse<unknown>).status(StatusCodes.REQUEST_TIMEOUT).json(
                createErrorResponse(
                    error.message,
                    'Request timed out'
                )
            );
            return;
        }

        if (error instanceof DatabaseError) {
            (res as TypedResponse<unknown>).status(StatusCodes.INTERNAL_SERVER_ERROR).json(
                createErrorResponse(
                    error.message,
                    error.details ?? 'Internal server error'
                )
            );
            return;
        }

        // 默认为内部服务器错误
        (res as TypedResponse<unknown>).status(StatusCodes.INTERNAL_SERVER_ERROR).json(
            createErrorResponse(
                defaultMessage,
                'Internal server error'
            )
        );
    }

    /**
     * 从请求中获取分页参数
     */
    protected getPaginationParams(req: PaginatedRequest): {
        page: number;
        limit: number;
        offset: number;
        sortBy: string;
        sortOrder: 'asc' | 'desc';
    } {
        const query = req.query as PaginationQuery;
        const page = parseInt(query.page ?? '1', 10);
        const limit = Math.min(parseInt(query.limit ?? '20', 10), 100);
        const offset = (page - 1) * limit;
        const sortBy = query.sortBy ?? 'createdAt';
        const sortOrder = query.sortOrder ?? 'desc';

        return {
            page,
            limit,
            offset,
            sortBy,
            sortOrder: sortOrder as 'asc' | 'desc',
        };
    }

    /**
     * 发送成功响应
     */
    protected sendSuccess<T>(res: TypedResponse<T>, data: T, message?: string, statusCode: number = 200): void {
        const response = createSuccessResponse(data, message);
        res.status(statusCode).json(response);
    }

    /**
     * 发送分页响应
     */
    protected sendPaginatedResponse<T>(
        res: TypedResponse<T[]>,
        data: T[],
        pagination: PaginationMetadata,
        message?: string,
        statusCode: number = 200
    ): void {
        const response = createPaginatedResponse(data, pagination, message);
        res.status(statusCode).json(response);
    }

    /**
     * 发送错误响应
     */
    protected sendError(
        res: TypedResponse<unknown>, 
        message: string, 
        details?: string | object, 
        statusCode: number = StatusCodes.BAD_REQUEST
    ): void {
        const response = createErrorResponse(message, details);
        res.status(statusCode).json(response);
    }

    /**
     * 从请求中获取用户ID
     */
    protected getUserId(req: AuthenticatedRequest): string {
        if (req.userId === undefined || req.userId === '') {
            throw new Error('User ID not found in request');
        }
        return req.userId;
    }

    /**
     * 从请求中获取认证用户
     */
    protected getUser(req: AuthenticatedRequest): { id: string; email?: string; roles?: string[] } {
        if (!req.user) {
            throw new Error('User not found in request');
        }
        return req.user;
    }

    /**
     * 从请求中获取请求ID
     */
    protected getRequestId(req: AuthenticatedRequest): string {
        return req.requestId ?? 'unknown';
    }

    /**
     * 验证必需的字段
     */
    protected validateRequiredFields(data: Record<string, unknown>, fields: string[]): void {
        const missingFields = fields.filter(field => {
            const value = data[field];
            return value === undefined || value === null || value === '';
        });
        if (missingFields.length > 0) {
            throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
        }
    }

    /**
     * 清理输入数据
     */
    protected sanitizeInput<T extends Record<string, unknown>>(data: T): T {
        const sanitized = { ...data };
        
        // 移除所有 null 或 undefined 的值
        Object.keys(sanitized).forEach(key => {
            if (sanitized[key] === null || sanitized[key] === undefined) {
                delete sanitized[key];
            }
        });

        return sanitized;
    }

    /**
     * 记录控制器操作
     */
    protected logAction(action: string, req: AuthenticatedRequest, details?: Record<string, unknown>): void {
        const logContext: LogContext = {
            requestId: this.getRequestId(req),
            userId: req.user?.id ?? 'anonymous',
            method: req.method,
            url: req.path,
            ...details,
        };

        Logger.info(`Controller action: ${action}`, logContext);
    }

    /**
     * 创建安全的日志上下文
     */
    protected createLogContext(req: AuthenticatedRequest, additionalContext?: Record<string, unknown>): LogContext {
        return {
            requestId: req.requestId ?? 'unknown',
            userId: req.user?.id ?? 'anonymous',
            method: req.method,
            url: req.path,
            ...additionalContext,
        };
    }
} 